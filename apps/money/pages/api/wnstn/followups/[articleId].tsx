import { NextApiRequest, NextApiResponse } from 'next';

interface QuestionsApiResponse {
  article_index: string;
  created_at: string;
  follow_up_questions: string[];
  node_id: string;
  post_id: number;
  status: string;
}

interface ErrorResponse {
  error: {
    message: string;
    code: string;
  };
}

const handler = async (req: NextApiRequest, res: NextApiResponse<QuestionsApiResponse | ErrorResponse>) => {
  if (req.method !== 'GET') {
    return res.status(405).json({
      error: {
        message: 'Method not allowed',
        code: 'METHOD_NOT_ALLOWED',
      },
    });
  }

  const { articleId } = req.query;

  if (!articleId || typeof articleId !== 'string') {
    return res.status(400).json({
      error: {
        message: 'Article ID is required',
        code: 'MISSING_ARTICLE_ID',
      },
    });
  }

  try {
    const response = await fetch(`https://editorial-tools.benzinga.com/api/v2/wnstn/followups/${articleId}`);

    if (!response.ok) {
      return res.status(response.status).json({
        error: {
          message: `External API error: ${response.statusText}`,
          code: 'EXTERNAL_API_ERROR',
        },
      });
    }

    const data: QuestionsApiResponse = await response.json();
    
    return res.status(200).json(data);
  } catch (error) {
    console.error('Error fetching WNSTN followup questions:', error);
    return res.status(500).json({
      error: {
        message: 'Internal server error',
        code: 'INTERNAL_SERVER_ERROR',
      },
    });
  }
};

export default handler;
