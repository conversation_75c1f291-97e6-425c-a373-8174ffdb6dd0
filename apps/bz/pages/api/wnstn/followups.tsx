import { NextApiRequest, NextApiResponse } from 'next';
import { getGlobalSession } from '../session';
import { ArticleManager } from '@benzinga/article-manager';

interface QuestionsApiResponse {
  article_index: string;
  created_at: string;
  follow_up_questions: string[];
  node_id: string;
  post_id: number;
  status: string;
}

interface ErrorResponse {
  error: {
    message: string;
    code: string;
  };
}

const handler = async (req: NextApiRequest, res: NextApiResponse<QuestionsApiResponse | ErrorResponse>) => {
  if (req.method !== 'GET') {
    return res.status(405).json({
      error: {
        message: 'Method not allowed',
        code: 'METHOD_NOT_ALLOWED',
      },
    });
  }

  const { articleId } = req.query;

  if (!articleId || typeof articleId !== 'string') {
    return res.status(400).json({
      error: {
        message: 'Article ID is required',
        code: 'MISSING_ARTICLE_ID',
      },
    });
  }

  try {
    const session = getGlobalSession();
    const articleManager = session.getManager(ArticleManager);
    const response = await articleManager.getFollowUpQuestions(articleId);

    if (response.err) {
      return res.status(500).json({
        error: {
          message: 'Failed to fetch follow-up questions',
          code: 'FETCH_ERROR',
        },
      });
    }

    return res.status(200).json(response.ok);
  } catch (error) {
    console.error('Error fetching WNSTN followup questions:', error);
    return res.status(500).json({
      error: {
        message: 'Internal server error',
        code: 'INTERNAL_SERVER_ERROR',
      },
    });
  }
};

export default handler;
