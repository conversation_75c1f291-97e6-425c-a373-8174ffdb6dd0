import { ArticleManager } from '@benzinga/article-manager';
import { NextApiRequest, NextApiResponse } from 'next';
import { getGlobalSession } from './session';

interface QuestionsApiResponse {
  article_index: string;
  created_at: string;
  follow_up_questions: string[];
  node_id: string;
  post_id: number;
  status: string;
}

interface ErrorResponse {
  error: {
    message: string;
  };
}

const handler = async (req: NextApiRequest, res: NextApiResponse<QuestionsApiResponse | ErrorResponse>) => {
  const { articleId } = req.query;

  if (!articleId || typeof articleId !== 'string') {
    return res.status(400).json({
      error: {
        message: 'Article ID is required',
      },
    });
  }

  try {
    const session = getGlobalSession();
    const articleManager = session.getManager(ArticleManager);
    // Use the internal method directly since this API route uses the internal editorial API
    const response = await articleManager.getFollowUpQuestionsInternal(articleId);

    if (response.err) {
      return res.status(500).json({
        error: {
          message: 'Failed to fetch follow-up questions',
        },
      });
    }

    return res.status(200).json(response.ok);
  } catch (error) {
    console.error('Error fetching WNSTN followup questions:', error);
    return res.status(500).json({
      error: {
        message: 'Internal server error',
      },
    });
  }
};

export default handler;
