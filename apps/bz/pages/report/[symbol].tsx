import React, { Suspense } from 'react';
import ReportSection from '../../src/components/ReportSection';
import Balancer from 'react-wrap-balancer';
import numeral from 'numeral';
import styled from '@benzinga/themetron';
import dayjs from 'dayjs';
import { FaCheck } from 'react-icons/fa';
import {
  StockReportsManager,
  GetReportDataResponse,
  General,
  Rating,
  Stock,
  SectionStatus,
} from '@benzinga/stock-reports-manager';
import { getGlobalSession } from '../api/session';
import type { EChartsOption } from 'echarts';
import { NextPageContext } from 'next';
import {
  addSection,
  getCandleColor,
  getCandle,
  MappedPeer,
  Section,
  Candle,
  TickerDetails,
  compareValues,
  parseFinancials,
  fetchCandles,
  fetchCompanyLogo,
  splitDescription,
  calculatePercentChange,
  mapPeerInfo,
  generateChartOption,
} from '../../src/components/Report';
import { BzImage } from '@benzinga/image';
import { useIsUserPaywalled } from '@benzinga/user-context';
import { useCallback, useEffect, useState } from 'react';

import { AuthContainer } from '@benzinga/auth-ui';

import { MetaProps, PageType } from '@benzinga/seo';
import { sanitizeHTML } from '@benzinga/frontend-utils';
const BzEdgeCTA = React.lazy(() => import('@benzinga/ads').then(module => ({ default: module.BzEdgeCTA })));
const EChart = React.lazy(() => import('../../src/components/EChart'));

interface StockReportPageProps {
  candles7day: Candle[];
  candles5Year: Candle[];
  candles60Days: Candle[];
  logoUrl: string;
  symbol: string;
}
interface AccumulatorCandle {
  total: number;
  count: number;
}

const StockReportPage: React.FC<StockReportPageProps> = props => {
  const paywall = useIsUserPaywalled('com/reports', '#', 'hard');
  const { candles5Year, candles7day, candles60Days, logoUrl, symbol } = props;

  const [tickerDetails, setTickerDetails] = useState<TickerDetails>();
  const [companyInfo, setCompanyInfo] = useState<General>();

  const [yesterdayCandle, setYesterdayCandle] = useState<Candle>();
  const [todayCandle, setTodayCandle] = useState<Candle>();
  const [yearHi, setYearHi] = useState(0);
  const [yearLo, setYearLo] = useState(0);

  const [updated, setUpdated] = useState('');
  const [chartOption, setChartOption] = useState<EChartsOption>();

  const getReportData = async (symbol: string): Promise<GetReportDataResponse | null> => {
    try {
      const reportResponse = await getGlobalSession().getManager(StockReportsManager).getReportData(symbol);
      const reportDataResponse: GetReportDataResponse | null = reportResponse?.ok ?? null;
      return reportDataResponse;
    } catch (error) {
      console.error('Error getting report data:', error);
    }
    return null;
  };

  const buildGeneralSection = useCallback(
    (
      data: Stock,
    ): {
      blurb: string;
      description: string;
      title: string;
      type: string;
    } => {
      const description = data?.general.company_description || '';
      const [blurb, remainingDescription] = splitDescription(description);

      return {
        blurb,
        description: remainingDescription,
        title: 'Company Description',
        type: 'general',
      };
    },
    [],
  );

  const buildGraphSection = useCallback((): {
    options: EChartsOption;
    title: string;
    type: string;
  } => {
    return {
      options: {
        graphic: [
          {
            bottom: '20%',
            right: '5%',
            style: {
              image: '/next-assets/images/Benzinga-logo-navy.svg',
              opacity: 0.3,
              width: 100,
            },
            type: 'image',
          },
        ],
        grid: {
          bottom: '3%',
          containLabel: true,
          left: '2%',
          right: '4%',
          top: '12%',
        },
        series: [
          {
            areaStyle: {
              color: {
                colorStops: [
                  { color: 'rgba(16, 96, 255, 0.3)', offset: 0 },
                  { color: 'rgba(255, 255, 255, 0)', offset: 1 },
                ],
                global: false,
                type: 'linear',
                x: 0,
                x2: 0,
                y: 0,
                y2: 1,
              },
            },
            data: candles60Days.map((candle: Candle) => {
              return candle.close;
            }),
            lineStyle: {
              color: 'rgb(16, 96, 255)',
              width: 2,
            },
            symbol: 'none',
            type: 'line',
          },
        ],
        xAxis: {
          axisLabel: {
            interval: 5,
          },
          boundaryGap: false,
          data: candles60Days.map((candle: Candle) => {
            const date = dayjs(candle.dateTime);
            return date.format('MMM D');
          }),
          min: 'dataMin',
          splitLine: {
            interval: 5,
            show: true,
          },
          type: 'category',
        },
        yAxis: {
          min: 'dataMin',
          splitLine: {
            show: false,
          },
          type: 'value',
        },
      },
      title: `${companyInfo?.company_name || symbol} (${symbol}) Stock Graph`,
      type: 'graph',
    };
  }, [candles60Days, companyInfo?.company_name, symbol]);

  const buildGradeSection = useCallback(
    (
      data: Stock,
    ): {
      options: EChartsOption;
      title: string;
      type: string;
    } => {
      return {
        options: {
          graphic: [
            {
              bottom: '25%',
              right: '40%',
              style: {
                image: '/next-assets/images/Benzinga-logo-navy.svg',
                opacity: 0.25,
                width: 60,
              },
              type: 'image',
            },
          ],
          grid: {
            bottom: '0%',
            containLabel: true,
            left: '0%',
            right: '0%',
            top: '5%',
          },
          radar: {
            indicator: [
              { max: 5, name: 'VALUE' },
              { max: 5, name: 'GROWTH' },
              { max: 5, name: 'HEALTH' },
              { max: 5, name: 'PAST' },
              { max: 5, name: 'DIVIDEND' },
            ],
            shape: 'circle',
          },
          series: [
            {
              data: [
                {
                  areaStyle: {
                    opacity: 0.1,
                  },
                  itemStyle: {
                    color: 'rgb(16, 96, 255)',
                  },
                  label: {
                    show: false,
                  },
                  value: [
                    data?.scores?.value || 0,
                    data?.scores?.growth || 0,
                    data?.scores?.health || 0,
                    data?.scores?.past || 0,
                    data?.scores?.dividend || 0,
                  ],
                },
              ],
              type: 'radar',
            },
          ],
        },
        title: `How We Grade ${companyInfo?.company_name || symbol} (${symbol})`,
        type: 'grade',
      };
    },
    [companyInfo?.company_name, symbol],
  );

  const buildTopPeersSection = useCallback((data: Stock) => {
    return {
      peers: data?.peer_info?.top_peers.map(mapPeerInfo),
      title: 'Peer Ratings',
      type: 'top_peers',
    };
  }, []);

  const buildGrowthSection = useCallback(
    (
      data: Stock,
    ): {
      average_dollars: number;
      change_percent: number;
      max_dollars: number;
      min_dollars: number;
      num_ratings: number;
      options: EChartsOption;
      ratings: Rating[];
      title: string;
      type: string;
    } => {
      const minDollars = data?.growth?.min_pt;
      const maxDollars = data?.growth?.max_pt;
      const averageDollars = data?.growth?.average_pt;
      const changePercent = data?.growth?.pct_chg_pt / 100;
      const recentRatings = data?.growth?.recent_ratings;

      const accumulators = new Map<string, AccumulatorCandle>();
      candles5Year.slice(-52).forEach((candle: Candle) => {
        const dateKey = dayjs(candle.dateTime).format("MMM 'YY");
        if (!accumulators.has(dateKey)) {
          accumulators.set(dateKey, { count: 0, total: 0 });
        }
        const accumulator = accumulators.get(dateKey);
        if (accumulator) {
          accumulator.total += candle.close;
          accumulator.count += 1;
        }
      });

      const yearAverages = new Map<string, number>();
      accumulators.forEach((accumulator, dateKey) => {
        yearAverages.set(dateKey, accumulator.total / accumulator.count);
      });

      const months: string[] = [];
      const averageValues: number[] = [];

      yearAverages.forEach((value, key) => {
        months.push(key);
        averageValues.push(value);
      });

      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

      const lastDate = months && months.length > 0 ? months[months.length - 1] : monthNames[new Date().getMonth()];
      const lastValue = months && months.length > 0 ? averageValues[averageValues.length - 1] : 0;

      if (lastDate) {
        while (!lastDate?.includes(monthNames[monthNames.length - 1])) {
          const shiftedName = monthNames.shift();

          if (shiftedName) {
            monthNames.push(shiftedName);
          }
        }
      }

      months.push(...monthNames);

      return {
        average_dollars: averageDollars,
        change_percent: changePercent || 0,
        max_dollars: maxDollars,
        min_dollars: minDollars,
        num_ratings: recentRatings?.length || 0,
        options: {
          graphic: [
            {
              bottom: '20%',
              left: '12%',
              style: {
                image: '/next-assets/images/Benzinga-logo-navy.svg',
                opacity: 0.3,
                width: 100,
              },
              type: 'image',
            },
          ],
          grid: {
            bottom: '3%',
            containLabel: true,
            left: '2%',
            right: '4%',
            top: '5%',
          },
          series: [
            {
              areaStyle: {
                color: {
                  colorStops: [
                    { color: 'rgba(16, 96, 255, 0.3)', offset: 0 },
                    { color: 'rgba(255, 255, 255, 0)', offset: 1 },
                  ],
                  global: false,
                  type: 'linear',
                  x: 0,
                  x2: 0,
                  y: 0,
                  y2: 1,
                },
              },
              data: averageValues,
              lineStyle: {
                color: 'rgb(16, 96, 255)',
                width: 2,
              },
              markLine: {
                data: [
                  [
                    {
                      coord: [lastDate, lastValue],
                      emphasis: {
                        label: {
                          show: false,
                        },
                      },
                      label: {
                        show: false,
                      },
                      symbol: 'none',
                      symbolSize: 20,
                    },
                    {
                      coord: [monthNames[monthNames.length - 1], maxDollars],
                      emphasis: {
                        label: {
                          show: false,
                        },
                      },
                      label: {
                        show: false,
                      },
                      lineStyle: {
                        color: 'rgb(19, 220, 76)',
                        width: 2,
                      },
                      symbol: 'circle',
                      symbolSize: 20,
                    },
                  ],
                  [
                    {
                      coord: [lastDate, lastValue],
                      emphasis: {
                        label: {
                          show: false,
                        },
                      },
                      label: {
                        show: false,
                      },
                      symbol: 'none',
                      symbolSize: 20,
                    },
                    {
                      coord: [monthNames[monthNames.length - 1], averageDollars],
                      emphasis: {
                        label: {
                          show: false,
                        },
                      },
                      label: {
                        show: false,
                      },
                      lineStyle: {
                        color: 'rgb(230, 234, 9)',
                        width: 2,
                      },
                      symbol: 'circle',
                      symbolSize: 20,
                    },
                  ],
                  [
                    {
                      coord: [lastDate, lastValue],
                      emphasis: {
                        label: {
                          show: false,
                        },
                      },
                      label: {
                        show: false,
                      },
                      symbol: 'none',
                      symbolSize: 20,
                    },
                    {
                      coord: [monthNames[monthNames.length - 1], minDollars],
                      emphasis: {
                        label: {
                          show: false,
                        },
                      },
                      label: {
                        show: false,
                      },
                      lineStyle: {
                        color: 'rgb(255, 0, 0)',
                        width: 2,
                      },
                      symbol: 'circle',
                      symbolSize: 20,
                    },
                  ],
                ],
              },
              markPoint: {
                data: [
                  {
                    coord: [monthNames[monthNames.length - 1], maxDollars],
                    itemStyle: {
                      color: '#FFF',
                    },
                    name: 'Max',
                    symbol: 'circle',
                    symbolSize: 10,
                  },
                  {
                    coord: [monthNames[monthNames.length - 1], averageDollars],
                    itemStyle: {
                      color: '#FFF',
                    },
                    name: 'Average',
                    symbol: 'circle',
                    symbolSize: 10,
                  },
                  {
                    coord: [monthNames[monthNames.length - 1], minDollars],
                    itemStyle: {
                      color: '#FFF',
                    },
                    name: 'Min',
                    symbol: 'circle',
                    symbolSize: 10,
                  },
                ],
              },
              symbol: 'none',
              type: 'line',
            },
          ],
          xAxis: {
            boundaryGap: false,
            data: months,
            type: 'category',
          },
          yAxis: {
            max: Math.max(...averageValues, maxDollars),
            min: Math.min(...averageValues, minDollars),
            type: 'value',
          },
        },
        ratings: recentRatings.slice(0, 9),
        title: 'Future Growth',
        type: 'growth',
      };
    },
    [candles5Year],
  );

  const buildRelativeValueSection = useCallback(
    (
      data: Stock,
    ): {
      callout: string;
      options: EChartsOption;
      pe_avg: number;
      pe_multiple: number;
      rank: string;
      title: string;
      type: string;
    } | null => {
      const values = data?.valuation?.eps_history?.map(e => Object.values(e)[0]);

      if (!values) {
        return null;
      }

      const years = data?.valuation?.eps_history?.map(e => Object.keys(e)[0]);

      let callout = '';

      if (values?.length > 2) {
        const direction = compareValues(values[0], values[1], values[2]);
        switch (direction) {
          case 'up':
            callout = `As you can see from the chart above, ${symbol}'s earnings have increased for the past three years, this is a positive sign for the stock.`;
            break;
          case 'down':
            callout = `As you can see from the chart above, ${symbol}'s earnings have decreased for the past three years, this is a negative sign for the stock.`;
            break;
          case 'stable':
            callout = `As you can see from the chart above, ${symbol}'s earnings held steady for the past three years, this is a neutral sign for the stock.`;
            break;
          case 'fluctuating_up':
            callout = `As you can see from the chart above, ${symbol}'s earnings have fluctuated but has increased since, this is a positive sign for the stock.`;
            break;
          case 'fluctuating_down':
            callout = `As you can see from the chart above, ${symbol}'s earnings have fluctuated but has decreased since, this is a negative sign for the stock.`;
            break;
          default:
            callout = '';
            break;
        }
      }

      return {
        callout,
        options: {
          graphic: [
            {
              bottom: '12%',
              right: '0%',
              style: {
                image: '/next-assets/images/Benzinga-logo-navy.svg',
                opacity: 0.3,
                width: 100,
              },
              type: 'image',
              z: 100,
            },
          ],
          grid: {
            bottom: '0%',
            containLabel: true,
            left: '0%',
            right: '0%',
            top: '2%',
          },
          series: [
            {
              backgroundStyle: {
                color: 'rgba(16, 96, 255, 0.1)',
              },
              data: values,
              itemStyle: {
                borderRadius: [5, 5, 0, 0],
                color: 'rgb(16, 96, 255)',
              },
              showBackground: true,
              type: 'bar',
            },
          ],
          xAxis: {
            data: years,
            type: 'category',
          },
          yAxis: {
            type: 'value',
          },
        },
        pe_avg: data?.peer_info?.pe_avg,
        pe_multiple: data?.valuation?.pe_multiple,
        rank: '23',
        title: 'Valuation',
        type: 'relative_value',
      };
    },
    [symbol],
  );

  const buildHealthSection = useCallback(
    (
      data: Stock,
    ): {
      debt_to_equity: number;
      debt_to_equity_options: EChartsOption;
      operating_margin: number;
      operating_margin_options: EChartsOption;
      quick_ratio: number;
      quick_ratio_options: EChartsOption;
      title: string;
      type: string;
    } => {
      const debtToEquity = data?.health?.debt_to_equity ?? 0;
      const debtToEquityAvg = data?.peer_info?.debt_to_equity_avg;
      const operatingMargin = data?.health?.operating_margin ?? 0;
      const operatingMarginAvg = data?.peer_info?.op_margin_avg;
      const quickRatio = data?.health?.quick_ratio ?? 0;
      const quickRatioAvg = data?.peer_info?.quick_ratio_avg;

      const waterMark = {
        right: '5%',
        style: {
          image: '/next-assets/images/Benzinga-logo-navy.svg',
          opacity: 0.2,
          width: 80,
        },
        top: '35%',
        type: 'image',
        z: 100,
      };

      return {
        debt_to_equity: debtToEquity,
        debt_to_equity_options: {
          graphic: [waterMark],
          grid: {
            bottom: '0%',
            containLabel: true,
            left: '0%',
            right: '2%',
            top: '0%',
          },
          series: [
            {
              backgroundStyle: {
                color: 'rgba(16, 96, 255, 0.1)',
              },
              barWidth: '50%',
              data: [debtToEquity],
              itemStyle: {
                borderRadius: [0, 5, 5, 0],
                color: 'rgb(16, 96, 255)',
              },
              markPoint: {
                data: [
                  {
                    coord: [debtToEquityAvg, 'a'],
                    itemStyle: {
                      color: 'rgb(41,60,85)',
                    },
                    name: 'Debt to Equity Average',
                    symbol: 'diamond',
                    symbolSize: 20,
                  },
                ],
              },
              showBackground: true,
              type: 'bar',
            },
          ],
          xAxis: {
            max: 0,
            min: Math.max(Math.ceil(debtToEquityAvg), Math.ceil(debtToEquity), 5),
            type: 'value',
          },
          yAxis: {
            boundaryGap: true,
            data: ['a'],
            show: false,
            type: 'category',
          },
        },
        operating_margin: operatingMargin,
        operating_margin_options: {
          graphic: [waterMark],
          grid: {
            bottom: '0%',
            containLabel: true,
            left: '0%',
            right: '2%',
            top: '0%',
          },
          series: [
            {
              backgroundStyle: {
                color: 'rgba(16, 96, 255, 0.1)',
              },
              barWidth: '50%',
              data: [operatingMargin * 100],
              itemStyle: {
                borderRadius: [0, 5, 5, 0],
                color: 'rgb(16, 96, 255)',
              },
              markPoint: {
                data: [
                  {
                    coord: [operatingMarginAvg * 100, 'a'],
                    itemStyle: {
                      color: 'rgb(41,60,85)',
                    },
                    name: 'Operating Margin Average',
                    symbol: 'diamond',
                    symbolSize: 20,
                  },
                ],
              },
              showBackground: true,
              type: 'bar',
            },
          ],
          xAxis: {
            max: 100,
            min: -100,
            type: 'value',
          },
          yAxis: {
            boundaryGap: true,
            data: ['a'],
            show: false,
            type: 'category',
          },
        },
        quick_ratio: quickRatio,
        quick_ratio_options: {
          graphic: [waterMark],
          grid: {
            bottom: '0%',
            containLabel: true,
            left: '0%',
            right: '2%',
            top: '0%',
          },
          series: [
            {
              backgroundStyle: {
                color: 'rgba(16, 96, 255, 0.1)',
              },
              barWidth: '50%',
              data: [quickRatio],
              itemStyle: {
                borderRadius: [0, 5, 5, 0],
                color: 'rgb(16, 96, 255)',
              },
              markPoint: {
                data: [
                  {
                    coord: [quickRatioAvg, 'a'],
                    itemStyle: {
                      color: 'rgb(41,60,85)',
                    },
                    name: 'Quick Ratio Average',
                    symbol: 'diamond',
                    symbolSize: 20,
                  },
                ],
              },
              showBackground: true,
              type: 'bar',
            },
          ],
          xAxis: {
            max: 0,
            min: Math.max(Math.ceil(quickRatioAvg), Math.ceil(quickRatio), 5),
            type: 'value',
          },
          yAxis: {
            boundaryGap: true,
            data: ['a'],
            show: false,
            type: 'category',
          },
        },
        title: 'Financial Health',
        type: 'health',
      };
    },
    [],
  );

  const buildDividendSection = useCallback(
    (
      data: Stock,
    ): {
      change_percent: number;
      first_year: string;
      options: EChartsOption;
      title: string;
      type: string;
    } => {
      const dividendData = data?.dividend.dividend_yields.map(e => {
        return Object.values(e)[0] * 100;
      });
      const dividendYears = data?.dividend.dividend_yields.map(e => Object.keys(e)[0]);
      return {
        change_percent: calculatePercentChange(dividendData[0], data?.dividend.current_yield * 100),
        first_year: dividendYears[0],
        options: {
          graphic: [
            {
              bottom: '20%',
              left: '8%',
              style: {
                image: '/next-assets/images/Benzinga-logo-navy.svg',
                opacity: 0.3,
                width: 100,
              },
              type: 'image',
            },
          ],
          grid: {
            bottom: '3%',
            containLabel: true,
            left: '2%',
            right: '4%',
            top: '5%',
          },
          series: [
            {
              areaStyle: {
                color: {
                  colorStops: [
                    { color: 'rgba(16, 96, 255, 0.3)', offset: 0 },
                    { color: 'rgba(255, 255, 255, 0)', offset: 1 },
                  ],
                  global: false,
                  type: 'linear',
                  x: 0,
                  x2: 0,
                  y: 0,
                  y2: 1,
                },
              },
              color: 'rgb(16, 96, 255)',
              data: dividendData,
              lineStyle: {
                color: 'rgb(16, 96, 255)',
                type: 'dashed',
                width: 2,
              },
              symbol: 'circle',
              symbolSize: 20,
              type: 'line',
            },
          ],
          xAxis: {
            boundaryGap: false,
            data: dividendYears,
            type: 'category',
          },
          yAxis: {
            type: 'value',
          },
        },
        title: 'Dividend',
        type: 'dividend',
      };
    },
    [],
  );

  const buildPastSection = useCallback(
    (
      data: Stock,
    ): {
      options: EChartsOption;
      peer_sharpe: number;
      sharpe: number;
      title: string;
      type: string;
    } => {
      return {
        options: {
          graphic: [
            {
              bottom: '20%',
              right: '5%',
              style: {
                image: '/next-assets/images/Benzinga-logo-navy.svg',
                opacity: 0.3,
                width: 100,
              },
              type: 'image',
            },
          ],
          grid: {
            bottom: '3%',
            containLabel: true,
            left: '2%',
            right: '4%',
            top: '5%',
          },
          series: [
            {
              areaStyle: {
                color: {
                  colorStops: [
                    { color: 'rgba(16, 96, 255, 0.3)', offset: 0 },
                    { color: 'rgba(255, 255, 255, 0)', offset: 1 },
                  ],
                  global: false,
                  type: 'linear',
                  x: 0,
                  x2: 0,
                  y: 0,
                  y2: 1,
                },
              },
              color: 'rgb(16, 96, 255)',
              data: candles5Year.map((candle: Candle) => {
                return candle.close;
              }),
              lineStyle: {
                color: 'rgb(16, 96, 255)',
                width: 2,
              },
              symbol: 'none',
              type: 'line',
            },
          ],
          xAxis: {
            boundaryGap: false,
            data: candles5Year.map((candle: Candle) => {
              const date = dayjs(candle.dateTime);
              return date.format('MMM');
            }),
            type: 'category',
          },
          yAxis: {
            type: 'value',
          },
        },
        peer_sharpe: data?.peer_info?.sharpe_avg,
        sharpe: data?.past?.sharpe,
        title: 'Past Performance',
        type: 'past',
      };
    },
    [candles5Year],
  );

  const buildOptionsSection = useCallback(
    (
      data: Stock,
    ): {
      options: EChartsOption;
      sentiment: string;
      title: string;
      type: string;
    } => {
      const positiveSentiment = Object.values(data?.auxiliary?.options?.options_sentiment).filter(e => e > 0).length;
      const negativeSentiment = Object.values(data?.auxiliary?.options?.options_sentiment).filter(e => e < 0).length;
      return {
        options: {
          graphic: [
            {
              bottom: '20%',
              left: '8%',
              style: {
                image: '/next-assets/images/Benzinga-logo-navy.svg',
                opacity: 0.3,
                width: 100,
              },
              type: 'image',
            },
          ],
          grid: {
            bottom: '3%',
            containLabel: true,
            left: '2%',
            right: '4%',
            top: '5%',
          },
          series: [
            {
              color: 'rgb(16, 96, 255)',
              data: Object.values(data?.auxiliary?.options?.options_sentiment),
              type: 'bar',
            },
          ],
          xAxis: {
            boundaryGap: false,
            data: Object.keys(data?.auxiliary?.options?.options_sentiment),
            type: 'category',
          },
          yAxis: {
            type: 'value',
          },
        },
        sentiment:
          positiveSentiment === negativeSentiment
            ? 'neutral'
            : positiveSentiment > negativeSentiment
              ? 'positive'
              : 'negative',
        title: `Analyzing ${companyInfo?.company_name || symbol} (${symbol}) Recent Options Activity`,
        type: 'options_sentiment',
      };
    },
    [companyInfo, symbol],
  );

  const buildFinancialsSection = useCallback(
    (
      data: Stock,
    ): {
      financials: {
        balance_sheet: (string | number)[][];
        cash_flow: (string | number)[][];
        income_statement: (string | number)[][];
      };
      title: string;
      type: string;
    } => {
      return {
        financials: {
          balance_sheet: data?.auxiliary?.financials
            ? parseFinancials(data?.auxiliary?.financials, 'Balance Sheet')
            : [],
          cash_flow: data?.auxiliary?.financials ? parseFinancials(data?.auxiliary?.financials, 'Cash Flow') : [],
          income_statement: data?.auxiliary?.financials
            ? parseFinancials(data?.auxiliary?.financials, 'Income Statement')
            : [],
        },
        title: 'Company Financials',
        type: 'financials',
      };
    },
    [],
  );

  const buildSections = useCallback(
    async (responseData: Stock, availableSections: SectionStatus, symbol: string) => {
      let details: TickerDetails = {
        _id: symbol,
        sections: [],
      };
      if (availableSections?.general) {
        details = addSection(details, buildGeneralSection(responseData));
      }

      if (candles60Days.length > 0) {
        details = addSection(details, buildGraphSection());
      }

      if (responseData?.scores) {
        details = addSection(details, buildGradeSection(responseData));
      }

      if (availableSections?.top_peers) {
        details = addSection(details, buildTopPeersSection(responseData));
      }

      if (availableSections?.growth) {
        details = addSection(details, buildGrowthSection(responseData));
      }

      if (availableSections?.relative_value) {
        const relativeValueSection = buildRelativeValueSection(responseData);
        if (relativeValueSection) {
          details = addSection(details, relativeValueSection);
        }
      }

      if (availableSections?.health) {
        details = addSection(details, buildHealthSection(responseData));
      }

      if (availableSections?.dividend) {
        details = addSection(details, buildDividendSection(responseData));
      }

      if (availableSections?.past) {
        details = addSection(details, buildPastSection(responseData));
      }

      if (availableSections?.options_sentiment) {
        details = addSection(details, buildOptionsSection(responseData));
      }

      if (availableSections?.financials) {
        details = addSection(details, buildFinancialsSection(responseData));
      }
      setTickerDetails(details);
    },
    [
      buildGeneralSection,
      buildGraphSection,
      buildGradeSection,
      buildTopPeersSection,
      buildGrowthSection,
      buildRelativeValueSection,
      buildHealthSection,
      buildDividendSection,
      buildPastSection,
      buildOptionsSection,
      buildFinancialsSection,
      candles60Days,
    ],
  );

  useEffect(() => {
    const init = async () => {
      const response = await getReportData(props.symbol);

      setYesterdayCandle(getCandle(candles60Days, candles60Days.length - 2));
      setTodayCandle(getCandle(candles60Days, candles60Days.length - 1));

      let newYearHi = 0;
      let newYearLo = Infinity;

      candles5Year.slice(-52).forEach((candle: Candle) => {
        newYearHi = Math.max(newYearHi, candle.high);
        newYearLo = Math.min(newYearLo, candle.low);
      });

      setYearHi(newYearHi);
      setYearLo(newYearLo);

      setCompanyInfo(response?.data?.general);

      const chartColor = getCandleColor(
        candles60Days[candles60Days.length - 2],
        candles60Days[candles60Days.length - 1],
      );
      setChartOption(generateChartOption(candles7day, chartColor));

      const updatedDate = response?.data?.updated ? response.data?.updated * 1000 : new Date().getTime();
      setUpdated(dayjs(updatedDate).format('MMM D, YYYY'));

      if (response?.data) {
        buildSections(response?.data, response?.section_status, symbol);
      }
    };

    init();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <HeaderBg className="flex flex-col w-full">
        <div className="flex justify-between py-2 px-3 lg:py-4 lg:px-6 w-full border-b border-bzblue-800">
          <div></div>
          <div className="bg-bzblue-900/80 text-white py-2 px-3 rounded-sm text-sm">
            <span className="text-bzblue-500 text-xs mr-2">UPDATED:</span> <strong>{updated}</strong>
          </div>
        </div>
        <div className="flex flex-col w-full lg:w-[780px] max-w-3xl text-bzblue-200 mt-4 mx-auto">
          <div className="text-center">
            <div className="text-[30px] lg:text-[70px] whitespace-nowrap font-bold">STOCK ANALYSIS</div>
          </div>
          <div className="flex flex-col m-4 lg:flex-row border border-bzblue-800 rounded-md">
            <div className="flex-1 p-4 flex border-r border-bzblue-800">
              {logoUrl && (
                <div className="flex justify-center items-center bg-white p-2 rounded-lg mr-4">
                  <BzImage
                    alt={`${symbol} Logo`}
                    height={50}
                    layout="fixed"
                    objectFit="contain"
                    src={logoUrl}
                    width={50}
                  />
                </div>
              )}
              <div className="flex-1 flex justify-between">
                {companyInfo && (
                  <>
                    <div>
                      <a
                        className="text-xl font-bold lg:font-medium lg:text-3xl text-bzblue-200 hover:cursor-pointer"
                        href={`/quote/${symbol}`}
                        target="_blank"
                      >
                        {companyInfo?.company_name || symbol}
                      </a>
                      <div className="text-sm text-bzblue-500">
                        {companyInfo?.exchange}:{symbol}
                      </div>
                    </div>
                  </>
                )}
                {todayCandle && yesterdayCandle && (
                  <div className="lg:hidden">
                    <div className="flex-1 flex flex-col justify-center items-center">
                      <div className="text-center">
                        <div className="text-2xl">{numeral(todayCandle.close).format('$0,0.00')}</div>
                        <div
                          className={`text-sm ${
                            todayCandle.close >= yesterdayCandle.close ? 'text-green-400' : 'text-red-400'
                          }`}
                        >
                          {numeral(todayCandle.close - yesterdayCandle.close).format('$0,0.00')} |{' '}
                          {numeral((todayCandle.close - yesterdayCandle.close) / yesterdayCandle.close).format(
                            '0,0.00%',
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="flex-1 flex">
              <div className="w-full lg:w-2/3">{chartOption && <EChart customOptions={chartOption} />}</div>
              {todayCandle && yesterdayCandle && (
                <div className="flex-1 flex-col justify-center items-center hidden lg:flex">
                  <div className="text-center">
                    <div className="text-2xl">{numeral(todayCandle.close).format('$0,0.00')}</div>
                    <div
                      className={`text-sm ${
                        todayCandle.close >= yesterdayCandle.close ? 'text-green-400' : 'text-red-400'
                      }`}
                    >
                      {numeral(todayCandle.close - yesterdayCandle.close).format('$0,0.00')} |{' '}
                      {numeral((todayCandle.close - yesterdayCandle.close) / yesterdayCandle.close).format('0,0.00%')}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="grid grid-cols-3 lg:grid-cols-5 text-sm mb-4 mx-6">
            {todayCandle && (
              <div className="flex lg:justify-center my-2">
                <div>
                  <div className="text-bzblue-500">Day Range:</div>
                  <div>
                    {numeral(todayCandle.low).format('$0,0.00')} - {numeral(todayCandle.high).format('$0,0.00')}
                  </div>
                </div>
              </div>
            )}
            {companyInfo?.market_cap && (
              <>
                <div className="flex lg:justify-center my-2">
                  <div>
                    <div className="text-bzblue-500">Market Cap:</div>
                    <div>{numeral(companyInfo?.market_cap).format('0.00a').toUpperCase()}</div>
                  </div>
                </div>
                <div className="flex lg:justify-center my-2">
                  <div>
                    <div className="text-bzblue-500">P/E Ratio:</div>
                    <div>{numeral(companyInfo?.pe).format('0,0.0000')}</div>
                  </div>
                </div>
              </>
            )}
            <div className="flex lg:justify-center my-2">
              <div>
                <div className="text-bzblue-500">Avg Value:</div>
                <div>{numeral((yearLo + yearHi) / 2).format('$0,0.00')}</div>
              </div>
            </div>
            <div className="flex lg:justify-center my-2">
              <div>
                <div className="text-bzblue-500">Year Range:</div>
                <div>
                  {numeral(yearLo).format('$0,0.00')} - {numeral(yearHi).format('$0,0.00')}
                </div>
              </div>
            </div>
          </div>
        </div>
      </HeaderBg>
      <Suspense>
        {paywall.active && (
          <AuthContainer
            authMode="register"
            // contentType="stock-report"
            iterationStyle="edge-hard"
            placement="report"
            preventRedirect={true}
          />
        )}
      </Suspense>
      {paywall.active && (
        <div className="relative mt-10 md:mt-20 z-10 animate-fade-up animate-delay-700 animate-once">
          <BzEdgeCTA type="stock-report" />
        </div>
      )}
      <div
        className={`flex flex-col items-center w-full p-2 lg:p-12 report text-bzblue-800 bg-bzblue-200 ${paywall.active ? 'blur-lg' : ''}`}
      >
        {tickerDetails?.sections?.map((section: Section, idx: number) => (
          <ReportSection key={idx} sectionIndex={idx + 1} title={section?.title ?? ''}>
            {section.type === 'general' && (
              <div className="flex bg-white">
                <div className="w-full h-full p-6">
                  <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">{section.blurb}</Balancer>
                  <p>{section.description}</p>
                </div>
                <div className="">
                  <div className="w-16 h-16"></div>
                </div>
              </div>
            )}
            {section.type === 'graph' && (
              <div className="flex bg-white">
                <div className="w-full h-[300px] p-4">
                  <EChart customOptions={section.options} />
                </div>
              </div>
            )}
            {section.type === 'grade' && (
              <div className="flex flex-col lg:flex-row items-center bg-white">
                <div className="flex flex-col w-full h-full p-6">
                  <p>
                    We grade stocks based on past performance, their future growth potential, intrinsic value, dividend
                    history, and overall financial health.
                  </p>
                  <p>
                    The chart below shows how we grade{' '}
                    <strong>
                      {companyInfo?.company_name || symbol} ({symbol})
                    </strong>{' '}
                    across the board compared to its closest peers.
                  </p>
                </div>
                <div className="flex flex-col items-center py-4">
                  <div className="w-[300px] h-[200px] mx-6">
                    <EChart customOptions={section.options} />
                  </div>
                </div>
              </div>
            )}
            {section.type === 'top_peers' && (
              <div className="grid grid-cols-2 bg-white">
                {section?.peers?.map((peer: MappedPeer, idx: number) => (
                  <div className="flex flex-col items-center py-4" key={idx}>
                    <div className="w-[300px] h-[200px] mx-6">
                      <EChart customOptions={peer.options} />
                    </div>
                    <div className="text-bzblue-800 text-center rounded-lg bg-bzblue-200 px-4 py-2">
                      <div className="text-lg font-bold">
                        {peer.symbol} : {peer.exchange}
                      </div>
                      <div className="text-sm">{peer.name}</div>
                    </div>
                  </div>
                ))}
              </div>
            )}
            {section.type === 'growth' && (
              <div className="bg-white">
                <div className="flex flex-col w-full h-full p-6">
                  <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
                    Our estimate of future price growth is based on{' '}
                    {section.num_ratings !== 1
                      ? 'an aggregation of ' + section.num_ratings + ' analyst ratings'
                      : section.num_ratings + ' analyst rating'}{' '}
                    over the past 3 months and their 12-month price targets.
                  </Balancer>
                  <p>
                    Below, you can see that analysts are estimating a 12-month price target range of{' '}
                    <strong>{numeral(section.min_dollars).format('$0,0.00')}</strong> -{' '}
                    <strong>{numeral(section.max_dollars).format('$0,0.00')}</strong> with an average of{' '}
                    <strong>{numeral(section.average_dollars).format('$0,0.00')}</strong>
                  </p>
                </div>
                <div className="w-full h-[300px] p-4">
                  <EChart customOptions={section.options} />
                </div>
                {section?.change_percent && (
                  <div
                    className={`flex items-center bg-white bg-gradient-to-r ${
                      section?.change_percent >= 0 ? 'from-green-100' : 'from-red-100'
                    }`}
                  >
                    <div className={`text-4xl p-4 ${section.change_percent >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                      {numeral(section.change_percent).format('0,0.00%')}
                    </div>
                    <div>
                      <div className="font-bold">
                        Expected movement for {companyInfo?.company_name || symbol} ({symbol}) over the next 12 months
                      </div>
                      <div>Based on these rankings</div>
                    </div>
                  </div>
                )}
                <div className="p-6">
                  <p>
                    Recent Ratings for {companyInfo?.company_name || symbol} ({symbol})
                  </p>
                  <div className="grid grid-cols-2 lg:grid-cols-3 gap-2">
                    {section?.ratings?.map((rating: Rating, idx: number) => (
                      <div className="rounded-md overflow-hidden border border-bzblue-400" key={'rating-' + idx}>
                        <div className="bg-bzblue-400 text-bzblue-800 font-bold py-2 px-4 text-sm">
                          {rating.firm_name}
                        </div>
                        <div className="p-4 text-sm">
                          <div className="flex mb-2 lg:mb-0 lg:justify-between flex-col lg:flex-row">
                            <div>Date:</div>
                            <div className="lg:text-right font-bold">{dayjs(rating.date).format('MMM D, YYYY')}</div>
                          </div>
                          <div className="flex mb-2 lg:mb-0 lg:justify-between flex-col lg:flex-row">
                            <div>Action:</div>
                            <div className="lg:text-right font-bold">{rating.action}</div>
                          </div>
                          <div className="flex mb-2 lg:mb-0 lg:justify-between flex-col lg:flex-row">
                            <div>Prev. Target:</div>
                            <div className="lg:text-right font-bold">{numeral(rating.pt_prior).format('$0,0.00')}</div>
                          </div>
                          <div className="flex mb-2 lg:mb-0 lg:justify-between flex-col lg:flex-row">
                            <div>New Target:</div>
                            <div className="lg:text-right font-bold">
                              {numeral(rating.pt_current).format('$0,0.00')}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
            {section.type === 'relative_value' && (
              <div className="bg-white">
                {/* <div className="w-full h-full p-6 border-b border-bzblue-400">
                  <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
                    Valuing {companyInfo?.company_name || symbol} ({symbol}) Relative to its Peers
                  </Balancer>
                  <p>
                    Another way of valuing a stock is to compare its Price/Earnings ratio to other stocks that are
                    similar in industry and size. This method is called <strong>relative valuation</strong>, and it
                    allows analysts to gauge the price that a stock should be trading at based on how it performs
                    relative to companies that are most similar to it.
                  </p>
                  <p>
                    Based on a comparison analysis against its closest peers,{' '}
                    <strong>
                      {companyInfo?.company_name || symbol} ({symbol})
                    </strong>{' '}
                    is trading at <strong>{section.pe_multiple}x</strong> the peer average of{' '}
                    <strong>{section.pe_avg}</strong>.
                  </p>
                </div> */}
                {/* <div className="w-full h-full p-6 border-b border-bzblue-400">
                  <p className="text-2xl mb-4">P/E Ranking Amongst Its Peers</p>
                  <div className="w-full h-[400px] p-4">
                    <EChart customOptions={section.options} />
                  </div>
                  <div className="flex items-center">
                    <div className="w-12 h-12 mr-4 bg-bzblue-300 flex justify-center items-center rounded-full text-bzblue-700 font-bold">
                      {section.rank}
                    </div>
                    <Balancer className="text-sm font-bold">
                      As you can see from the chart above, {symbol}&apos;s earnings have increased recently, this is a
                      positive sign for the stock.
                    </Balancer>
                  </div>
                </div> */}
                <div className="w-full h-full p-6">
                  <p className="text-2xl mb-4">Earnings History (3 years)</p>
                  <Balancer className="text-sm font-bold">
                    It is important to look at a companies earnings history to see not only if they are profitable, but
                    if their earnings are growing.
                  </Balancer>
                  <div className="w-full h-[400px] p-4">
                    <EChart customOptions={section.options} />
                  </div>
                  <div className="flex items-center">
                    <div className="w-12 h-12 mr-4 bg-bzblue-300 flex justify-center items-center rounded-full text-bzblue-700 font-bold">
                      <FaCheck />
                    </div>
                    <Balancer className="text-sm font-bold">{section.callout}</Balancer>
                  </div>
                </div>
              </div>
            )}
            {section.type === 'health' && (
              <div className="bg-white">
                <div className="w-full h-full p-6 border-b border-bzblue-400">
                  <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
                    What is the current state of the company&apos;s financial situation?
                  </Balancer>
                  <p>
                    We measure the health of a company based on how profitable they are and their ability to cover both
                    their short-term and long-term debts. The key indicators that we use are the Operating Margin, Quick
                    Ratio, and Debt-to-Equity ratio relative to the companies peers
                  </p>
                </div>
                <div className="w-full h-full p-6 border-b border-bzblue-400">
                  <p className="text-2xl mb-4">
                    Operational Margin <strong>{numeral(section.operating_margin).format('0,0.0000')}</strong>
                  </p>
                  <div className="w-full h-[140px] p-4">
                    <EChart customOptions={section.operating_margin_options} />
                  </div>
                  <p>
                    The operating margin measures how much profit a company makes after it spends money on wages,
                    materials or other administrative expenses but before interest and taxes. It is a good
                    representation of how efficiently a company is able to generate profit from its core operations.
                  </p>
                </div>
                <div className="w-full h-full p-6 border-b border-bzblue-400">
                  <p className="text-2xl mb-4">
                    Quick Ratio <strong>{numeral(section.quick_ratio).format('0,0.0000')}</strong>
                  </p>
                  <div className="w-full h-[140px] p-4">
                    <EChart customOptions={section.quick_ratio_options} />
                  </div>
                  <p>
                    The quick ratio measures how much of a company&apos;s debt, that is due in less than 1 year, can be
                    covered using its cash equivalents, marketable securities, and money that is currently owed to them
                    (accounts receivables).
                  </p>
                  <p>
                    A company with a quick ratio of less than 1.00 does not, in many cases, have the capital on hand to
                    meet its short-term obligations if they were all due at once, while a quick ratio greater than one
                    indicates the company has the financial resources to remain solvent in the short term.
                  </p>
                </div>
                <div className="w-full h-full p-6 border-b border-bzblue-400">
                  <p className="text-2xl mb-4">
                    Debt-to-Equity <strong>{numeral(section.debt_to_equity).format('0,0.0000')}</strong>
                  </p>
                  <div className="w-full h-[140px] p-4">
                    <EChart customOptions={section.debt_to_equity_options} />
                  </div>
                  <p>
                    Debt-to-equity is calculated by dividing a company&apos;s total liabilities by its shareholders
                    equity. It is a measure of the degree to which a company is financing its operations through debt
                    versus wholly owned funds. Generally speaking, a D/E ratio below 1.0 would be seen as relatively
                    safe, whereas ratios of 2.0 or higher would be considered risky.
                  </p>
                  <div className="flex items-center">
                    <div className="w-12 h-12 mr-4 bg-bzblue-300 flex justify-center items-center rounded-full text-bzblue-700 font-bold">
                      <FaCheck />
                    </div>
                    <Balancer className="text-sm font-bold">
                      The chart above shows {companyInfo?.company_name || symbol} ({symbol}) operating margin, quick
                      ratio, and debt-to-equity ratio compared to its peers. The black markers represent the peer
                      averages for each ratio and the blue bars represent {companyInfo?.company_name || symbol} (
                      {symbol}) ratio values.
                    </Balancer>
                  </div>
                </div>
              </div>
            )}
            {section.type === 'dividend' && (
              <div className="bg-white">
                <div className="flex flex-col w-full h-full p-6">
                  <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
                    There are few things we like to see when evaluating the quality of a company&apos;s dividend history
                    and future
                  </Balancer>
                  <p>
                    Ideally, we would like to see a company have a long history of consistently high dividend payouts
                    that have grown at a consistent rate. From here we want to be confident that this sort of dividend
                    growth and consistency will persist into the future.
                  </p>
                  <p className="font-bold">
                    The chart below shows the historical trend in {companyInfo?.company_name || symbol} ({symbol})
                    dividend yield on an annual basis.
                  </p>
                </div>
                <div className="w-full h-[300px] p-4">
                  <EChart customOptions={section.options} />
                </div>
                {section.change_percent && (
                  <div
                    className={`flex items-center bg-white bg-gradient-to-r ${
                      section.change_percent >= 0 ? 'from-green-100' : 'from-red-100'
                    }`}
                  >
                    <div className={`text-4xl p-4 ${section.change_percent >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                      {numeral(section.change_percent / 100).format('0,0.00%')}
                    </div>
                    <div>
                      <div className="font-bold">
                        {companyInfo?.company_name || symbol} ({symbol}) saw a{' '}
                        {section.change_percent >= 0 ? 'increase' : 'decrease'} in it&apos;s dividend payouts since{' '}
                        {section.first_year}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
            {section.type === 'past' && (
              <div className="bg-white">
                <div className="flex flex-col w-full h-full p-6">
                  <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
                    How has {companyInfo?.company_name || symbol} ({symbol}) performed over the past 5 years?
                  </Balancer>
                  <p>
                    The two main factors that we consider when analyzing past performance is overall return and
                    volatility
                  </p>
                </div>
                <div className="w-full h-[300px] p-4">
                  <EChart customOptions={section.options} />
                </div>
                <div className="flex flex-col w-full h-full p-6">
                  <p>
                    Using these two metrics, we can determine if this stock gave its investors enough return for the
                    risk that they took on by owning it. This is measured by the sharpe ratio, which has been used as a
                    primary measure of risk/reward trade-off for almost 60 years.
                  </p>
                  <p>
                    This ratio can be interpreted as the amount of return an investor has received for the amount of
                    risk that they took on by owning the stock over that timeframe.{' '}
                  </p>
                </div>
                <div className="border-t border-bzblue-200 flex items-center">
                  <div className="p-6">
                    <p className="font-bold">
                      {companyInfo?.company_name || symbol} ({symbol}) sharpe ratio over the past 5 years is{' '}
                      {numeral(section.sharpe).format('0,0.0000')} which is considered to be{' '}
                      {section?.sharpe && section.peer_sharpe && section.sharpe > section.peer_sharpe
                        ? 'above '
                        : 'below '}
                      average compared to the peer average of {numeral(section.peer_sharpe).format('0,0.0000')}
                    </p>
                  </div>
                </div>
              </div>
            )}
            {section.type === 'options_sentiment' && (
              <div className="bg-white">
                <div className="flex flex-col w-full h-full p-6">
                  <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
                    Below, you can see the trend in options sentiment over the past 30 days.
                  </Balancer>
                  {section.sentiment === 'positive' && (
                    <p>
                      Based on our data, {symbol}&apos;s options trades have recently carried more positive sentiment
                      than negative.
                    </p>
                  )}
                  {section.sentiment === 'negative' && (
                    <p>
                      Based on our data, {symbol}&apos;s options trades have recently carried more negative sentiment
                      than positive.
                    </p>
                  )}
                  {section.sentiment === 'neutral' && (
                    <p>
                      Based on our data, {symbol}&apos;s options trades have recently carried more neutral sentiment.
                    </p>
                  )}
                </div>
                <div className="w-full h-[300px] p-4">
                  <EChart customOptions={section.options} />
                </div>
              </div>
            )}
            {section.type === 'financials' && (
              <div className="bg-white">
                <div className="flex flex-col w-full h-full p-6">
                  <div className="pb-4 font-bold">INCOME STATEMENT</div>
                  <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
                    The income statement also known as the profit and loss statement, primarily focuses on the
                    company&apos;s revenues and expenses during a particular period.
                  </Balancer>
                  <p>
                    The main purpose of an income statement is to convey details of profitability and business
                    activities. Below, is {symbol}&apos;s income statement for the previous four years along with its
                    trailing-twelve- month profit & loss.
                  </p>
                </div>
                <div>
                  <div className="grid grid-cols-8">
                    <div className="bg-bzblue-700 text-white py-1 font-bold col-span-3 pl-2 lg:pl-6">
                      FISCAL YEAR (BIL.)
                    </div>
                    <div className="bg-bzblue-700 text-white py-1 font-bold">2019 ($)</div>
                    <div className="bg-bzblue-700 text-white py-1 font-bold">2020 ($)</div>
                    <div className="bg-bzblue-700 text-white py-1 font-bold">2021 ($)</div>
                    <div className="bg-bzblue-700 text-white py-1 font-bold">2022 ($)</div>
                    <div className="bg-bzblue-700 text-white py-1 font-bold">TTM ($)</div>
                  </div>
                  {section?.financials?.income_statement?.map((incomeStatement: (string | number)[], idx: number) => (
                    <div className={`grid grid-cols-8${idx % 2 === 0 ? ' bg-bzblue-300' : ''}`} key={idx}>
                      <div className="col-span-3 pl-2 lg:pl-6 pr-4 border-b border-bzblue-300 py-1 text-sm">
                        <Balancer>{incomeStatement[0]}</Balancer>
                      </div>
                      <div className="border-b border-bzblue-300 py-1 text-sm">
                        {numeral(incomeStatement[1]).format('0,0.00')}
                      </div>
                      <div className="border-b border-bzblue-300 py-1 text-sm">
                        {numeral(incomeStatement[2]).format('0,0.00')}
                      </div>
                      <div className="border-b border-bzblue-300 py-1 text-sm">
                        {numeral(incomeStatement[3]).format('0,0.00')}
                      </div>
                      <div className="border-b border-bzblue-300 py-1 text-sm">
                        {numeral(incomeStatement[4]).format('0,0.00')}
                      </div>
                      <div className="border-b border-bzblue-300 py-1 text-sm">
                        {numeral(incomeStatement[5]).format('0,0.00')}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="flex flex-col w-full h-full p-6">
                  <div className="pb-4 font-bold">BALANCE SHEET</div>
                  <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
                    The balance sheet is a snapshot of a companies financials during a particular period in time.
                  </Balancer>
                  <p>
                    It breaks down what acompany owns (assets) and what a company owes (liabilities), in order to give
                    investors an overview of its capital structure.
                  </p>
                </div>
                <div>
                  <div className="grid grid-cols-8">
                    <div className="bg-bzblue-700 text-white py-1 font-bold col-span-3 pl-2 lg:pl-6">
                      FISCAL YEAR (BIL.)
                    </div>
                    <div className="bg-bzblue-700 text-white py-1 font-bold">2019 ($)</div>
                    <div className="bg-bzblue-700 text-white py-1 font-bold">2020 ($)</div>
                    <div className="bg-bzblue-700 text-white py-1 font-bold">2021 ($)</div>
                    <div className="bg-bzblue-700 text-white py-1 font-bold">2022 ($)</div>
                    <div className="bg-bzblue-700 text-white py-1 font-bold">TTM ($)</div>
                  </div>
                  {section?.financials?.balance_sheet?.map((balanceSheet: (string | number)[], idx: number) => (
                    <div className={`grid grid-cols-8${idx % 2 === 0 ? ' bg-bzblue-300' : ''}`} key={idx}>
                      <div className="col-span-3 pl-2 lg:pl-6 pr-4 border-b border-bzblue-300 py-1 text-sm">
                        <Balancer>{balanceSheet[0]}</Balancer>
                      </div>
                      <div className="border-b border-bzblue-300 py-1 text-sm">
                        {numeral(balanceSheet[1]).format('0,0.00')}
                      </div>
                      <div className="border-b border-bzblue-300 py-1 text-sm">
                        {numeral(balanceSheet[2]).format('0,0.00')}
                      </div>
                      <div className="border-b border-bzblue-300 py-1 text-sm">
                        {numeral(balanceSheet[3]).format('0,0.00')}
                      </div>
                      <div className="border-b border-bzblue-300 py-1 text-sm">
                        {numeral(balanceSheet[4]).format('0,0.00')}
                      </div>
                      <div className="border-b border-bzblue-300 py-1 text-sm">
                        {numeral(balanceSheet[5]).format('0,0.00')}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="flex flex-col w-full h-full p-6">
                  <div className="pb-4 font-bold">CASH FLOW STATEMENTS</div>
                  <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
                    A companies statement of cash flows gives an investor a break down of the cash inflows and outflows
                    from acompanies operations and investment activities.
                  </Balancer>
                </div>
                <div>
                  <div className="grid grid-cols-8">
                    <div className="bg-bzblue-700 text-white py-1 font-bold col-span-3 pl-2 lg:pl-6">
                      FISCAL YEAR (BIL.)
                    </div>
                    <div className="bg-bzblue-700 text-white py-1 font-bold">2019 ($)</div>
                    <div className="bg-bzblue-700 text-white py-1 font-bold">2020 ($)</div>
                    <div className="bg-bzblue-700 text-white py-1 font-bold">2021 ($)</div>
                    <div className="bg-bzblue-700 text-white py-1 font-bold">2022 ($)</div>
                    <div className="bg-bzblue-700 text-white py-1 font-bold">TTM ($)</div>
                  </div>
                  {section?.financials?.cash_flow?.map((cashFlow: (string | number)[], idx: number) => (
                    <div className={`grid grid-cols-8${idx % 2 === 0 ? ' bg-bzblue-300' : ''}`} key={idx}>
                      <div className="col-span-3 pl-2 lg:pl-6 pr-4 border-b border-bzblue-300 py-1 text-sm">
                        <Balancer>{cashFlow[0]}</Balancer>
                      </div>
                      <div className="border-b border-bzblue-300 py-1 text-sm">
                        {cashFlow[1] ? numeral(cashFlow[1]).format('0,0.00') : '—'}
                      </div>
                      <div className="border-b border-bzblue-300 py-1 text-sm">
                        {cashFlow[2] ? numeral(cashFlow[2]).format('0,0.00') : '—'}
                      </div>
                      <div className="border-b border-bzblue-300 py-1 text-sm">
                        {cashFlow[3] ? numeral(cashFlow[3]).format('0,0.00') : '—'}
                      </div>
                      <div className="border-b border-bzblue-300 py-1 text-sm">
                        {cashFlow[4] ? numeral(cashFlow[4]).format('0,0.00') : '—'}
                      </div>
                      <div className="border-b border-bzblue-300 py-1 text-sm">
                        {cashFlow[5] ? numeral(cashFlow[5]).format('0,0.00') : '—'}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </ReportSection>
        ))}
      </div>
    </>
  );
};

export default StockReportPage;

export const getServerSideProps = async function (ctx: NextPageContext) {
  const { symbol } = ctx.query;
  const sanitizedSymbol = symbol ? sanitizeHTML(symbol as string) : '';
  const symbolUpperCase = sanitizedSymbol?.toString()?.toUpperCase() || 'AAPL';
  const metaProps: MetaProps = {
    canonical: `https://www.benzinga.com/reports/${symbolUpperCase}`,
    description: '',
    image:
      'https://cdn.benzinga.com/files/imagecache/bz2_opengraph_meta_image_400x300/sites/all/themes/bz2/images/bz-icon.png',
    pageType: PageType.Tool,
    title: `(${symbolUpperCase}) Stock Analysis Report`,
  };

  try {
    const [candles7day, candles5Year, candles60Days, logoUrl] = await Promise.all([
      fetchCandles(symbolUpperCase, -7, 'day', '5M'),
      fetchCandles(symbolUpperCase, -5, 'year', '1W'),
      fetchCandles(symbolUpperCase, -60, 'day', '1D'),
      fetchCompanyLogo(symbolUpperCase),
    ]);

    return {
      props: {
        candles5Year,
        candles60Days,
        candles7day,
        logoUrl,
        metaProps,
        symbol: symbolUpperCase,
      },
    };
  } catch (error) {
    console.error('Error getting candles data:', error);
  }

  return {
    props: {
      metaProps,
      symbol: symbolUpperCase,
    },
  };
};

const HeaderBg = styled.div`
  background: url('/next-assets/images/headerbg.jpg') top center no-repeat;
  background-size: cover;
`;
