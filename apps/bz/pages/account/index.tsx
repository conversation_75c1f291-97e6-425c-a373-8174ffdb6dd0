import React from 'react';
import { GetServerSideProps } from 'next';
import { StreamChat } from 'stream-chat';
import { useRouter } from 'next/router';
import Script from 'next/script';
import { getLanguageCodeByHost, setLanguageByHost } from '@benzinga/translate';
import styled from '@benzinga/themetron';
import { Avatar } from '@benzinga/core-ui';
import { TabType, TabContent, TabNavItem, TabContextProvider } from '@benzinga/ui';
import { Meta, MetaProps, PageType } from '@benzinga/seo';
import { capitalize, runningClientSide } from '@benzinga/utils';

import { getGlobalSession } from '../api/session';
import { AuthenticationManager, User } from '@benzinga/session';
import { SessionContext } from '@benzinga/session-context';
import { useIsUserLoggedIn } from '@benzinga/user-context';
import { UserManager, UserManagerEvent } from '@benzinga/user-manager';
import { Chat<PERSON><PERSON><PERSON>, ChatManager } from '@benzinga/chat-manager';
import { Card, ShopManager, ShopManagerEvent } from '@benzinga/shop-manager';
import { UserSubscription, SubscriptionsManager, SubscriptionsManagerEvent } from '@benzinga/subscription-manager';
import { TrackingManager } from '@benzinga/tracking-manager';

import { faUser } from '@fortawesome/pro-regular-svg-icons/faUser';
import { faCreditCard } from '@fortawesome/pro-solid-svg-icons/faCreditCard';
import { faMessage } from '@fortawesome/pro-regular-svg-icons';
import { useTranslation } from 'react-i18next';
import { faBell } from '@fortawesome/pro-solid-svg-icons/faBell';

import NewsAlerts from '../../src/components/Account/NewsAlerts';
import BillingTab, { BillingUserSubscription } from '../../src/components/Account/Billing';
import ProfileTab from '../../src/components/Account/Profile';

const ChatTab = React.lazy(() => import('../../src/components/Account/Chat'));

export interface AccountSettingsProps {
  chat: ChatIdentity;
  creditCards: Card[];
  metaProps: MetaProps;
  subscriptionsArr: UserSubscription[];
  tab: string;
  user: User;
}

export const getMetaProps = async tab => {
  const host = runningClientSide() ? window.location.hostname : 'en';
  const translations = await setLanguageByHost(host, ['common', 'profile']);
  const language = getLanguageCodeByHost(host);
  return {
    canonical: `https://www.benzinga.com/account${tab ? `/${tab}` : ''}`,
    description: '',
    image:
      'https://cdn.benzinga.com/files/imagecache/bz2_opengraph_meta_image_400x300/sites/all/themes/bz2/images/bz-icon.png',
    language: language,
    pageType: PageType.Tool,
    title: `My Account ${tab ? capitalize(tab) : ''} Settings - Benzinga`,
    translations: translations as Record<string, unknown> | undefined,
  };
};

export const AccountSettings = ({
  chat,
  creditCards,
  metaProps,
  subscriptionsArr,
  tab,
  user,
}: AccountSettingsProps) => {
  // console.log('user', user);
  const { t } = useTranslation(['profile', 'common']);
  const AccountTabs: TabType[] = [
    {
      href: '/account',
      icon: faUser,
      title: t('Profile.profile-info', { ns: 'profile' }),
    },
    {
      href: '/account/billing',
      icon: faCreditCard,
      title: t('Billing.label', { ns: 'profile' }),
    },
    {
      href: '/account/chat',
      icon: faMessage,
      title: t('Chat.label', { ns: 'profile' }),
    },
    {
      href: '/account/news-alerts',
      icon: faBell,
      title: t('NewsAlerts.label', { ns: 'profile' }),
    },
  ];
  const router = useRouter();
  const preSelectedTab = tab ? AccountTabs.find(t => t.href === router.asPath) : null;
  const [activeTab, setActiveTab] = React.useState<TabType>(preSelectedTab ?? AccountTabs[0]);
  const [profileUser, setProfileUser] = React.useState<User | null>(user ?? null);
  const [meta, setMeta] = React.useState<MetaProps | undefined>(metaProps);

  const [subscriptions, setSubscriptions] = React.useState<BillingUserSubscription[]>(subscriptionsArr ?? []);
  const [chatIdentity, setChatIdentity] = React.useState<ChatIdentity>(chat ?? null);
  const [chatClient, setChatClient] = React.useState<StreamChat | null>(null);
  const [mutedUser, setMutedUser] = React.useState<any>(null);
  const [cards, setCards] = React.useState<Card[]>(creditCards ?? []);

  const isLoggedIn = useIsUserLoggedIn();
  const session = React.useContext(SessionContext);
  const authenticationManager = session.getManager(AuthenticationManager);
  const subscriptionManager = session.getManager(SubscriptionsManager);
  const userManager = session.getManager(UserManager);
  const shopManager = session.getManager(ShopManager);

  React.useEffect(() => {
    if (!isLoggedIn) {
      const currentDomain = window.location.origin;
      router.push(`/login?next=${currentDomain}/account`);
    }
  }, [isLoggedIn, router, session]);

  React.useEffect(() => {
    const getPropsClientSide = async () => {
      const userReq = await session.getManager(AuthenticationManager).getAuthSession(true);
      if (userReq?.ok) {
        setProfileUser(userReq.ok.user);
      } else {
        const currentDomain = window.location.origin;
        router.push(`login?next=${currentDomain}/account`);
      }

      const tab = router.asPath.split('/').pop();
      const metaProps = await getMetaProps(tab === 'account' ? '' : tab);
      setMeta(metaProps);
      session.getManager(TrackingManager).setMeta(metaProps);

      const chatIdentity = await session.getManager(ChatManager).getIdentity();
      if (chatIdentity?.ok) {
        setChatIdentity(chatIdentity.ok);
      }
      const subscriptions = await subscriptionManager.getSubscriptions();
      if (subscriptions?.ok) {
        setSubscriptions(subscriptions.ok);
      }
      const creditCards = await session.getManager(ShopManager).getCreditCards();
      if (creditCards?.ok) {
        setCards(creditCards.ok);
      }
    };

    if (!user && isLoggedIn) {
      getPropsClientSide();
    }
  }, [isLoggedIn, router, session, subscriptionManager, user]);

  React.useEffect(() => {
    const setupStreamChat = async () => {
      const streamToken = session.getManager(ChatManager).getStreamKey();
      const chatClient = StreamChat.getInstance(streamToken, { timeout: 10000 });
      await chatClient
        .connectUser(
          {
            id: chatIdentity?.identityUuid,
          },
          chatIdentity?.streamToken,
        )
        .then(() => {
          const mutedUsers = (chatClient?.user?.mutes as any[]).map(mutedUser => ({
            action: mutedUser.target?.id,
            added: mutedUser?.created_at,
            expires: mutedUser?.expires ?? '',
            key: mutedUser.target?.id,
            user: mutedUser.target?.nickname,
          }));
          setMutedUser(mutedUsers);
          setChatClient(chatClient);
        })
        .catch(err => {
          console.error('Error connecting to chat', err);
        });
    };
    if (chatIdentity) {
      setupStreamChat();
    }
  }, [chatIdentity, session]);

  React.useEffect(() => {
    const userSubscription = userManager.subscribe((event: UserManagerEvent) => {
      if (event.type === 'user:user_update' && event.user && event.user?.accessType !== 'anonymous') {
        setProfileUser(event.user);
      }
    });
    const productSubscriptions = subscriptionManager.subscribe((event: SubscriptionsManagerEvent) => {
      if (event.type === 'subscription:user_subscriptions_changed' && event.userSubscriptions) {
        setSubscriptions(event.userSubscriptions);
      }
      if (event.type === 'delete_subscriptions' && event.uuid) {
        setSubscriptions(subscriptions =>
          subscriptions.map(sub => {
            if (sub.uuid === event.uuid) {
              sub.cancelAtPeriodEnd = true;
            }
            return sub;
          }),
        );
      }
      if (event.type === 'restart_subscriptions' && event.subscription) {
        setSubscriptions(subscriptions =>
          subscriptions.map(sub => {
            if (sub.uuid === event.subscription.uuid) {
              sub.cancelAtPeriodEnd = false;
            }
            return sub;
          }),
        );
      }
    });
    const creditCardSubscriptions = shopManager.subscribe((event: ShopManagerEvent) => {
      if (event.type === 'add_credit_cards') {
        setCards([...cards, event.card]);
      } else if (event.type === 'delete_credit_card') {
        setCards(cards.filter(card => card.uuid !== event.creditCardId));
      }
    });

    return () => {
      userSubscription.unsubscribe();
      productSubscriptions.unsubscribe();
      creditCardSubscriptions.unsubscribe();
    };
  }, [session, authenticationManager, userManager, subscriptionManager, cards, shopManager]);

  const handleTabChange = async (tab: TabType) => {
    history.pushState(null, '', tab.href);
    const metaProps = await getMetaProps(tab.href === '/account' ? '' : tab.title);
    setMeta(metaProps);
    session.getManager(TrackingManager).setMeta(metaProps);
  };

  return (
    <PageWrapper>
      <Script src="https://js.chargebee.com/v2/chargebee.js" strategy="beforeInteractive"></Script>
      {meta && <Meta {...meta} canonical={meta.canonical} />}
      <div className="main-container">
        <div className="main-wrapper">
          <TabContextProvider activeTab={activeTab} setActiveTab={setActiveTab}>
            <div className="tabs-wrapper">
              <div className="account-info">
                <div className="account-info__avatar">
                  <Avatar
                    imageClassName="account-info__image"
                    name={user?.displayName ?? ''}
                    photo={chatIdentity?.avatar ?? ''}
                    pixels={100}
                  />
                </div>
                <div className="account-info__name">
                  {profileUser?.firstName} {profileUser?.lastName}
                </div>
              </div>
              {AccountTabs.map((tab, index) => (
                <TabNavItem key={index} {...tab} onChange={handleTabChange} tabs={AccountTabs} />
              ))}
            </div>
            <div className="tab-content">
              <TabContent title={t('Profile.profile-info', { ns: 'profile' })}>
                <ProfileTab user={profileUser} />
              </TabContent>
              <TabContent title={t('Billing.label', { ns: 'profile' })}>
                <BillingTab
                  creditCards={cards}
                  subscriptionManager={subscriptionManager}
                  subscriptions={subscriptions}
                />
              </TabContent>
              <TabContent title={t('Chat.label', { ns: 'profile' })}>
                {chatIdentity && (
                  <ChatTab
                    chatClient={chatClient}
                    chatIdentity={chatIdentity}
                    mutedUsers={mutedUser}
                    setChatIdentity={setChatIdentity}
                    setMutedUsers={setMutedUser}
                    username={user?.displayName}
                  />
                )}
              </TabContent>
              <TabContent title="News Alerts">
                <NewsAlerts />
              </TabContent>
            </div>
          </TabContextProvider>
        </div>
      </div>
      <style global jsx>{`
        /* This is a hack to bring the credit card popconfirm to the top layer. */
        .ant-popover-placement-top {
          z-index: 3 !important;
        }
      `}</style>
    </PageWrapper>
  );
};

const PageWrapper = styled.div`
  background-color: #f8fafb;
  position: relative;

  .account-info {
    margin-top: 6px;
    display: flex;
    align-items: center;
    gap: 16px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-bottom: 16px;

    .account-info__name {
      font-size: 23px;
      font-weight: 700;
      white-space: pre-wrap;
      text-transform: capitalize;
    }
  }

  .account-info__image {
    border-radius: 50%;
    height: 100px;
    width: 100px;
  }

  .main-container {
    position: relative;
    /* z-index: 100; */
  }

  .main-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    @media (min-width: 800px) {
      padding: 0 10px;
      padding-top: 60px;
      padding-bottom: 20px;
    }
    @media (max-width: 800px) {
      padding: 0px;
    }
    width: 100%;
    display: flex;

    @media (max-width: 900px) {
      flex-direction: column;
    }

    @media (min-width: 900px) {
      flex-direction: row;
    }
  }

  .security-button {
    display: flex;
    align-items: center;
  }

  .compact-radio-image {
    background-image: url('/next-assets/images/compact-chat-msg.png');
    background-size: cover;
    height: 80px;
    width: 120px;
    border-radius: 5px;
  }

  .clean-radio-image {
    background-image: url('/next-assets/images/clean-chat-msg.png');
    background-size: cover;
    height: 80px;
    width: 120px;
    border-radius: 5px;
  }

  .checkbox-label {
    font-size: 14px;
    line-height: 20px;
    color: #000;
    cursor: pointer;
  }

  .setting-checkbox {
    height: 16px;
    width: 16px;
    border-radius: 3px;
    border: 1px solid #e4ebee;
    cursor: pointer;
    margin-top: 2px;

    :checked {
      background-color: #017eff;
      border: none;
    }
  }

  .setting-radio {
    height: 16px;
    width: 16px;
    border-radius: 50%;
    border: 1px solid #e4ebee;
    cursor: pointer;
    margin-top: 2px;

    :checked {
      background-color: #017eff;
      border: none;
    }

    :not(:checked) {
      background-color: #fff;
    }
  }

  .radio-label {
    font-size: 14px;
    line-height: 20px;
    color: #000;
    cursor: pointer;
  }

  .radio-image-label {
    text-align: center;
  }

  .active-radio-image {
    border: 3px solid #017eff;
  }

  .security-row {
    :not(:last-child) {
      border-bottom: 1px solid #e4ebee;
      padding-bottom: 20px;
      margin-bottom: 20px;
    }
  }

  .tabs-wrapper {
    /* We want all child li elements to not have the bullet */
    @media (max-width: 900px) {
    }
    @media (min-width: 900px) {
      max-width: 310px;
    }
    width: 100%;
    background-color: #fff;
    padding: 14px 20px;
    border-radius: 10px;

    box-shadow: 0 0 0 1px #e4ebee;

    @media (min-width: 800px) {
      margin-right: 50px;
    }

    > li {
      list-style: none;
      padding: 10px;
      border: 1px solid #f2f2f2;
      border-radius: 5px;
      background-color: #fff;
      display: flex;
      align-items: center;
      gap: 16px;
      font-size: 14px;
      color: black;

      &.active {
        background-color: #e4ebee;
        /* Add an inner box shadow that is slightly gray */
        box-shadow: inset 0 0 5px #ccc;
        color: #017eff;
      }

      &:hover {
        box-shadow: inset 0 0 3px #ccc;
        cursor: pointer;
      }

      .title {
        font-size: 14px;
        font-weight: 500;
        line-height: '125%';
        letter-spacing: 0.05em;
      }

      > span {
        &.active {
          font-weight: 500 !important;
        }
      }

      &:not(:last-child) {
        margin-bottom: 8px;
      }
    }
  }

  .tab-content {
    width: 100%;
    height: 100%;

    .button-holder {
      display: flex;
      justify-content: flex-end;
    }

    .account-button {
      display: flex;
      background: transparent;
      border: 1px solid #e0e7eb;
      border-radius: 50px;
      color: #96abb6;
      cursor: pointer;
      height: 28px;
      gap: 6px;

      .button-text {
        font-weight: 500;
        font-size: 14px;
      }

      :hover {
        background: #017eff;
        color: #fff;
      }
    }

    .account-save-error {
      color: red;
      font-size: 14px;
      margin-top: 10px;
    }
  }
`;

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  const host = req.headers.host as string;
  const translations = await setLanguageByHost(host, ['common', 'profile']); // set language for server side translations
  const language = getLanguageCodeByHost(host);
  try {
    const token = req.cookies['benzinga_token'];
    const session = getGlobalSession();

    if (token) {
      session.getManager(AuthenticationManager).setBenzingaToken(token);
    }
    const user = await session.getManager(AuthenticationManager).getAuthSession();

    const metaProps = {
      canonical: `https://www.benzinga.com/account`,
      description: '',
      image:
        'https://cdn.benzinga.com/files/imagecache/bz2_opengraph_meta_image_400x300/sites/all/themes/bz2/images/bz-icon.png',
      language,
      pageType: PageType.Tool,
      title: `My Account Settings - Benzinga`,
      translations,
    };

    if (!token || !user?.ok || user?.ok?.user?.accessType === 'anonymous') {
      return {
        props: {
          chat: null,
          creditCards: [],
          metaProps: metaProps,
          subscriptionsArr: [],
          tab: '',
          user: null,
        },
      };
    }

    const chatIdentity = await session.getManager(ChatManager).getIdentity(true);
    const subscriptions = await session.getManager(SubscriptionsManager).getSubscriptions(true);
    const creditCards = await session.getManager(ShopManager).getCreditCards();
    const cleanSubscriptions = subscriptions.ok?.map(sub => {
      for (const key in sub) {
        if (sub[key] === undefined) {
          sub[key] = null;
        }
      }
      return sub;
    });
    user.ok.user.subscriptions = cleanSubscriptions;

    return {
      props: {
        chat: chatIdentity?.ok ?? null,
        creditCards: creditCards?.ok ?? [],
        headerProps: {
          hideQuoteBar: true,
        },
        metaProps: metaProps,
        subscriptionsArr: cleanSubscriptions ?? [],
        tab: 'profile',
        user: user.ok?.user,
      },
    };
  } catch (err) {
    console.log('Error fetching account settings', err);
    return {
      redirect: {
        destination: '/',
        permanent: false,
      },
    };
  }
};

export default AccountSettings;
