import React, { useEffect, useState } from 'react';
import { ErrorsBox, Icon } from '@benzinga/core-ui';
import { AuthInput } from '../AuthComponents';
import { Authentication, AuthenticationManager } from '@benzinga/session';
import { SessionContext } from '@benzinga/session-context';
import { SafeType } from '@benzinga/safe-await';
import { faEdit } from '@fortawesome/pro-regular-svg-icons';
import { AuthMode } from '@benzinga/session';

export interface OTPProps {
  authentication?: SafeType<Authentication> | null;
  phoneNumber?: string;
  onVerified?: (auth: SafeType<Authentication>, authFor: string) => void;
  onError?: (errors: string | string[] | null) => void;
  setAuthMode?: (authMode: AuthMode) => void;
  isProfileVerification?: boolean;
  onProfileVerified?: () => void;
}
interface State {
  OTP: string;
  changePhone: boolean;
  error: string;
  errorMsg: string;
  isCodeSend: boolean;
  isSendingOTP: boolean;
  isVerifyingOTP: boolean;
  phoneNumber: string;
  registerAuth: SafeType<Authentication> | null;
}

export const SMSVerify = ({
  authentication,
  isProfileVerification,
  onProfileVerified,
  onVerified,
  phoneNumber,
  setAuthMode,
}: OTPProps) => {
  const [state, setState] = useState<State>({
    OTP: '',
    changePhone: false,
    error: '',
    errorMsg: '',
    isCodeSend: false,
    isSendingOTP: false,
    isVerifyingOTP: false,
    phoneNumber: phoneNumber || '',
    registerAuth: null,
  });

  const session = React.useContext(SessionContext);
  const authManager = session.getManager(AuthenticationManager);
  const incompleteOTP = state.OTP.trim() === '' && state.OTP.length < 4;
  const [isDisabled, setIsDisabled] = useState(false);
  const [countdown, setCountdown] = useState(0);

  const handleOnVerified = async () => {
    if (authentication?.ok) {
      if (onVerified) {
        onVerified(authentication, 'register');
      }
    } else if (isProfileVerification && onProfileVerified) {
      onProfileVerified();
    }
  };

  const handleOTPChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setState(prevState => ({ ...prevState, [name]: value }));
  };

  const handlePhoneVerification = async (e: { preventDefault: () => void }) => {
    e.preventDefault();
    setState(old => ({ ...old, isSendingOTP: true }));
    setIsDisabled(true);
    setCountdown(15);

    const interval = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(interval);
          setIsDisabled(false);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    try {
      const res = await authManager.smsRequest(state.phoneNumber);
      if (res?.err) {
        setState(old => ({ ...old, errorMsg: res.err?.message, isCodeSend: false, isSendingOTP: false }));
      } else {
        setState(old => ({ ...old, isCodeSend: true, isSendingOTP: false }));
      }
    } catch (error) {
      setState(old => ({ ...old, isSendingOTP: false }));
    }
  };

  const handleConfirmOTP = async (e: { preventDefault: () => void }) => {
    e.preventDefault();
    setState({ ...state, error: '', isVerifyingOTP: true });
    try {
      if (!incompleteOTP) {
        const token: string = state.OTP || '';
        const isVerified = await authManager.smsConfirm(token);
        if (isVerified.ok) {
          handleOnVerified();
          setAuthMode && setAuthMode('complete');
        } else {
          setState({ ...state, error: 'Your verification code is incorrect', isVerifyingOTP: false });
        }
      } else {
        setState({ ...state, error: 'Your verification code is incorrect', isVerifyingOTP: false });
      }
    } catch (error) {
      setState({ ...state, error: 'Your verification code is incorrect', isVerifyingOTP: false });
    }
  };

  const handleChangeNumber = e => {
    e.preventDefault();
    setState(prevState => ({
      ...prevState,
      changePhone: true,
    }));
  };

  useEffect(() => {
    setState(prevState => ({
      ...prevState,
      phoneNumber: phoneNumber || '',
    }));
  }, [phoneNumber]);

  return (
    <div>
      <div className="pt-8">
        <div className="mb-8">
          <h1 className="text-white">Phone Verification</h1>
          <p>Make sure you can receive messages on this number. Phone carrier charges may apply.</p>
        </div>
        {state.isCodeSend ? (
          <div className="flex flex-col">
            <AuthInput
              name="OTP"
              onChange={handleOTPChange}
              placeholder="Verification code"
              type="number"
              value={state.OTP}
            />
            {state.error && <ErrorsBox error={state.error} errors={null} includeKeys={true} />}
            <div className="flex gap-4 mt-4">
              <button
                className={'bg-[#3f83f8] text-white p-3 hover:bg-[#3268c4] border border-white rounded-md w-full'}
                disabled={isDisabled || state.isSendingOTP}
                onClick={handlePhoneVerification}
              >
                {countdown !== 0 && <div className="text-[10px] mr-1">Wait {countdown} secs to</div>}
                {!state.isSendingOTP ? 'Resend code' : 'Sending code'}
              </button>
              <button
                className="bg-[#3f83f8] text-white p-3 hover:bg-[#3268c4] border border-white rounded-md w-full disabled:opacity-30"
                disabled={state.OTP.length === 0}
                onClick={handleConfirmOTP}
                type="submit"
              >
                {!state.isVerifyingOTP ? 'Confirm' : 'Verifying...'}
              </button>
            </div>
          </div>
        ) : (
          <div className="flex flex-col" onSubmit={handlePhoneVerification}>
            <div className="relative">
              <AuthInput
                disabled={!state.changePhone}
                label="Phone Number"
                name="phoneNumber"
                onChange={handleOTPChange}
                placeholder="****************"
                type="tel"
                value={state.phoneNumber}
              />
              <button
                className="absolute text-black top-0 right-0 h-full w-16 flex items-center justify-center disabled:opacity-30"
                disabled={state.changePhone}
                onClick={handleChangeNumber}
              >
                <Icon className="label-icon text-2xl" icon={faEdit} />
              </button>
            </div>
            {state.errorMsg && <span className="text-xs text-red-500">{state.errorMsg}</span>}
            <div className="flex gap-4 mt-4">
              <button
                className="bg-[#3f83f8] text-white p-3 hover:bg-[#3268c4] border border-white rounded-md w-full"
                onClick={handlePhoneVerification}
              >
                {!state.isSendingOTP ? 'Send code' : 'Sending code'}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
