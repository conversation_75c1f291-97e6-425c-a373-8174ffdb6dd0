'use client';
import React, { useEffect, useState } from 'react';
import { TrackingManager, AuthEventType } from '@benzinga/tracking-manager';
import { Authentication, AuthenticationManager, AuthMode } from '@benzinga/session';
import { SessionContext } from '@benzinga/session-context';
import { SafeType } from '@benzinga/safe-await';

import { UserManager } from '@benzinga/user-manager';
import { useIsUserLoggedIn } from '@benzinga/user-context';
import { useHydrate } from '@benzinga/hooks';

import { AccountCreationIterations } from '../AuthIterations/AccountCreationIterations';
import { EdgeHardIterations } from '../AuthIterations/EdgeHardIterations';
import { EdgeSoftIterations } from '../AuthIterations/EdgeSoftIterations';
import { DefaultIteration } from '../AuthIterations/DefaultIteration';
import { AuthIterationType } from '../AuthIterations/types';
import { formatRedirectUrl, isProDomain, preventRedirectToLogin } from '../AuthIterations/utils';

import { HOST_LOCALE_MAPPING } from '@benzinga/translate';
import { NotificationManager } from '@benzinga/notification-manager';
import { SophiIterations } from '../AuthIterations/SophiIterations';

const subdomainsToRedirect = ['pro', 'probeta', ...Object.keys(HOST_LOCALE_MAPPING)];

export const AuthContainer = ({
  authMode = 'login',
  contentType,
  email,
  iterationStyle = 'default',
  iterationVersion,
  nextUrl,
  onLogin,
  onRegister,
  onSessionLoad,
  placement,
  preventRedirect,
  setShowPaywall,
}: AuthIterationType) => {
  const session = React.useContext(SessionContext);
  const [mounted, setMounted] = useState(false);
  const [isLoadingSession, setIsLoadingSession] = useState(false);
  const [iframe, setIframe] = useState<boolean>(false);
  const [mode, setMode] = useState<AuthMode | string | string[]>(authMode);
  const [phoneNumber, setPhoneNumber] = useState<string | undefined>(undefined);
  const userIsLoggedIn = useHydrate(useIsUserLoggedIn(), false);

  const [searchParams, setSearchParams] = useState<any>({});

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      const entries = Array.from(params.entries());
      const paramsObject = Object.fromEntries(entries);
      setSearchParams(paramsObject);
      if (paramsObject.iframe) {
        setIframe(paramsObject.iframe === 'true');
      }
    }
  }, []);

  useEffect(() => {
    session.getManager(TrackingManager).updateContext({ isPaywalled: true });
    return () => {
      session.getManager(TrackingManager).updateContext({ isPaywalled: false });
    };
  }, [session]);

  const getRedirectUrl = React.useCallback(
    async (forceUrl?: string, isSocial?: boolean): Promise<string> => {
      // check if user has completed onboarding
      const userManager = session.getManager(UserManager);
      const user = await userManager.getUser();
      const localization = user?.profileData?.subdomain ?? null;

      const userLayoutList = user?.layout_list;
      const isUserOnBoarded = await userManager.getGlobalSettings('isUserOnBoarded');

      const domain = window?.location?.hostname?.match(/[^.]+\.[^.]+$/)?.[0] || 'benzinga.com';
      const sub = window.location.hostname.split('.')[0];
      const subdomain = subdomainsToRedirect.includes(sub) ? sub : 'www';

      // default redirect url
      const defaultUrl = `https://www.${domain}/profile/portfolio/`;

      // the referrer
      const referrer = document.referrer;

      // the parent url or current url
      let parentUrl;

      try {
        parentUrl =
          window.location.href !== window?.parent?.location?.href ? window?.parent?.location?.href : undefined;
      } catch (e) {
        // use document.referrer for cross domain scenarios ex. pro.benzinga.com
        parentUrl = document.referrer;
      }

      // redirect after register
      const registerNextParam = searchParams.registernext;
      const registerNext = Array.isArray(registerNextParam) ? registerNextParam[0] : registerNextParam;

      // redirect after login
      const loginNextParam = searchParams.loginnext;
      const loginNext = Array.isArray(loginNextParam) ? loginNextParam[0] : loginNextParam;

      // redirect after login or register
      const redirectNextParam = searchParams.next || searchParams.redirect;
      const redirectNext = Array.isArray(redirectNextParam) ? redirectNextParam[0] : redirectNextParam;

      let redirectUrl =
        forceUrl || // force a specific url
        (mode === 'login' ? loginNext : registerNext) || // try login or register next depending on auth mode
        redirectNext || // try general next
        nextUrl || // try nextUrl prop
        referrer || // try referrer
        parentUrl || // try iframe parent url
        defaultUrl; // fallback to default url

      // cleanup redirect url
      redirectUrl = formatRedirectUrl(redirectUrl, defaultUrl);
      redirectUrl = preventRedirectToLogin(redirectUrl, defaultUrl);

      // check if the redirect url is a pro domain
      const isRedirectPro = isProDomain(redirectUrl);

      if (isSocial) {
        // if is social auth, no need to attach welcome until user is redirected back
        redirectUrl = `https://${subdomain}.${domain}/login?social=success&is_paywalled=${iterationStyle !== 'default' ? 'true' : 'false'}&next=${encodeURIComponent(redirectUrl)}&iframe=${iframe}`;
      } else if (!isRedirectPro && !isUserOnBoarded.ok && !userLayoutList?.length) {
        // if user is not onboarded, and it's not redirecting to pro, and user has no existing layouts redirect to welcome page
        redirectUrl = `https://${subdomain}.${domain}/welcome?next=${encodeURIComponent(redirectUrl)}`;
      }
      if (localization && localization !== 'benzinga') {
        redirectUrl = `https://${localization}.benzinga.com`;
      }

      return redirectUrl;
    },
    [mode, searchParams, session, iframe, nextUrl, iterationStyle],
  );

  const onSocialClicked = React.useCallback(
    async (socialPath: string, forceUrl?: string) => {
      // get the redirect url
      let redirectUrl = await getRedirectUrl(forceUrl, true);

      // url encode the redirect url
      redirectUrl = encodeURIComponent(redirectUrl);

      // open the social auth url and pass along the redirect url
      window.open(
        `https://accounts.benzinga.com/oauth/login/${socialPath}/?embedded=${
          iframe ? 'true' : 'false'
        }&next=${redirectUrl}`,
        '_top',
      );
    },
    [getRedirectUrl, iframe],
  );

  const handleSuccessfulAuth = React.useCallback(
    async (authSession: SafeType<Authentication>, type: string, backend?: AuthEventType) => {
      // get the redirect url
      const redirectUrl = await getRedirectUrl();

      // call onSessionLoad callback
      onSessionLoad && onSessionLoad(authSession);
      setPhoneNumber(authSession?.ok?.user?.phoneNumber);

      if (preventRedirect || authMode === 'sms-verify' || authMode === 'complete') {
        return;
      }

      const isPaywall = searchParams.is_paywalled === 'true';

      const backendType =
        searchParams.backend === 'google-oauth2'
          ? 'google'
          : searchParams.backend === 'google-one-tap'
            ? 'google-one-tap'
            : searchParams.backend === 'microsoft-graph'
              ? 'microsoft'
              : searchParams.backend === 'apple-id'
                ? 'apple'
                : backend;

      if (backendType) {
        session.getManager(TrackingManager).trackAuthEvent(type === 'register' ? 'register' : 'login', {
          auth_type: backendType,
          is_paywalled: isPaywall,
        });
      }

      if (iframe) {
        // if within an iframe, we'll send a message to the parent window
        console.log('sending message to parent window', {
          next: redirectUrl,
          type: `${type}_succeed`,
          user: authSession?.ok?.user,
        });
        window.parent.postMessage({ next: redirectUrl, type: `${type}_succeed`, user: authSession?.ok?.user }, '*');
      } else {
        if ('Notification' in window && Notification.permission === 'granted') {
          await session.getManager(NotificationManager).registerPushDevice();
        }
        // if not in an iframe we'll redirect the user
        window.open(redirectUrl, '_top');
      }
    },
    [authMode, getRedirectUrl, onSessionLoad, iframe, preventRedirect, session, searchParams],
  );

  const onLoginSuccess = React.useCallback(
    async (auth: SafeType<Authentication>, authFor: string) => {
      await handleSuccessfulAuth(auth, authFor, 'email');
      if (onLogin) {
        await onLogin(auth, authFor);
      }
    },
    [handleSuccessfulAuth, onLogin],
  );

  const onRegisterSuccess = React.useCallback(
    async (auth: SafeType<Authentication>, authFor: string) => {
      await handleSuccessfulAuth(auth, authFor, 'email');
      if (onRegister) {
        await onRegister(auth, authFor);
      }
    },
    [handleSuccessfulAuth, onRegister],
  );

  const getSession = React.useCallback(async () => {
    if (!isLoadingSession) {
      setIsLoadingSession(true);

      // pass in true to force a refresh of the session, otherwise it'll keep reference to the anonymous user
      const authSession = await session.getManager(AuthenticationManager).getAuthSession(true);

      if (authSession.err) {
        // console.error(authSession.err);
      } else if (authSession.ok?.user?.accessType !== 'anonymous') {
        const newUser = searchParams.new_user;
        await handleSuccessfulAuth(authSession, newUser === 'False' ? 'login' : 'register');
      }

      setIsLoadingSession(false);
    }
  }, [handleSuccessfulAuth, isLoadingSession, session, searchParams]);

  useEffect(() => {
    setMounted(true);
    if (!mounted) {
      getSession();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    session.getManager(TrackingManager).trackFormEvent('view', 'login_register', {
      form_type: iterationStyle,
    });
  }, [session, iterationStyle]);

  const showInsiderCopy = () => {
    return iframe && searchParams.content_type === 'insider-trades' ? 'insider-trades' : undefined;
  };

  if (!mounted) {
    return null;
  }

  if (iterationStyle === 'account-creation') {
    return (
      <AccountCreationIterations
        allowClose={false}
        authMode={mode}
        contentType={contentType}
        email={email}
        iterationVersion={iterationVersion}
        onLogin={onLoginSuccess}
        onRegister={onRegisterSuccess}
        onSessionLoad={onSessionLoad}
        onSocialClicked={onSocialClicked}
        phoneNumber={phoneNumber}
        placement={placement}
        setAuthMode={setMode}
      />
    );
  } else if (iterationStyle === 'edge-hard') {
    return (
      <EdgeHardIterations
        allowClose={false}
        authMode={mode}
        contentType={contentType}
        email={email}
        iterationVersion={iterationVersion}
        onLogin={onLoginSuccess}
        onRegister={onRegisterSuccess}
        onSessionLoad={onSessionLoad}
        onSocialClicked={onSocialClicked}
        phoneNumber={phoneNumber}
        placement={placement}
        setAuthMode={setMode}
        setShowPaywall={setShowPaywall}
      />
    );
  } else if (iterationStyle === 'edge-soft') {
    return (
      <EdgeSoftIterations
        allowClose={true}
        authMode={mode}
        contentType={contentType}
        email={email}
        iterationVersion={iterationVersion}
        onLogin={onLoginSuccess}
        onRegister={onRegisterSuccess}
        onSessionLoad={onSessionLoad}
        onSocialClicked={onSocialClicked}
        phoneNumber={phoneNumber}
        setAuthMode={setMode}
        setShowPaywall={setShowPaywall}
      />
    );
  } else if (iterationStyle === 'sophi') {
    return (
      <SophiIterations
        allowClose={true}
        authMode={mode}
        contentType={contentType}
        placement={placement}
        setShowPaywall={setShowPaywall}
      />
    );
  }

  return (
    <DefaultIteration
      allowClose={false}
      authMode={mode}
      contentType={showInsiderCopy()}
      email={email}
      onLogin={onLoginSuccess}
      onRegister={onRegisterSuccess}
      onSessionLoad={onSessionLoad}
      onSocialClicked={onSocialClicked}
      phoneNumber={phoneNumber}
      placement={placement}
      setAuthMode={setMode}
    />
  );
};
