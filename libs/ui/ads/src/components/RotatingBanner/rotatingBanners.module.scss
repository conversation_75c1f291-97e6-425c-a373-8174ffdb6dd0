.edge-logo {
  max-width: 254px;
  max-height: 27px;
  @media (max-width: 800px) {
    max-width: 140px;
    max-height: 20px;
  }
}

.webinar-v2 {
  background: #000000;

  .banner {
    .content  {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      padding-right: 1rem;
      @media (max-width: 800px) {
        gap: 0.25rem;
      }
    }
    .banner-header {
      color: white;
      font-family: Inter, Manrope, Manrope-fallback sans-serif;
      font-size: 28px;
      font-weight: 700;
      line-height: 35px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      text-transform: uppercase;

      @media (max-width:870px) {
        font-size: 14px;
      }
      @media (max-width: 800px) {
        font-size: 10px;
        line-height: 10px;
        letter-spacing: 0.1em;
      }
    }
    p {
      color: white;
      font-size: 28px;
      line-height: 30px;
      font-weight: 700;
      text-transform: uppercase;
      padding-bottom: 0.25rem;
      font-family: <PERSON>, <PERSON><PERSON><PERSON>, Manrope-fallback, sans-serif;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      flex-wrap: wrap;
      gap: 0.5rem;

      @media (max-width: 1443px) {
        font-size: 24px;
        line-height: 26px;
      }
      @media (max-width: 1270px) {
        font-size: 20px;
        line-height: 18px;
      }
      @media (max-width: 1040px) {
        font-size: 18px;
        gap: 0.25rem;
      }
      @media (max-width: 800px) {
        font-size: 12px;
        line-height: 14px;
      }
    }
    .button-wrapper {
      align-items: center;
      justify-content: center;
      @media (max-width: 800px) {
        background-color: transparent;
      }
    }

    .banner-button {
      background-color: #00C9FB;
      color: white;
      white-space: nowrap;
      text-transform: uppercase;
      font-family: Inter, Manrope, Manrope-fallback, sans-serif;
      font-size: 20px;
      font-weight: 800;
      line-height: 26px;
      letter-spacing: 0.08em;
      border-radius: 10px !important;
      padding: 0.75rem 1rem !important;

      box-shadow: 2.23px 4.83px 8.32px 0px #FFFFFF40 inset !important;
      filter: drop-shadow(0 0 0.5rem rgba(255, 255, 255, 0.50));

      @media (max-width: 1000px) {
        font-size: 16px;
        line-height: 20px
      }

      @media(max-width: 800px) {
        font-size: 10px;
        line-height: 14px;
        white-space: wrap;
        text-align: center;
      }
    }
  }

  .banner-bg-end {
    position: absolute;
    right: 0;
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
    width: 944px;
    height: 80px;
    @media (max-width: 944px) {
      width: 100%;
      background-size: cover;
    }
  }
}

.edge-v21 {
  background-color: #130035;

  .banner-image {
    object-position: left;
  }

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    p {
      color: white;
      font-size: 36px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, Manrope, Manrope-fallback, sans-serif;
      margin-right: 1rem;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      gap: 1rem;
      position: relative;
      flex-wrap: wrap;
     >div {
        position:relative;
        &::after {
          content: '';
          position: absolute;
          bottom: -10px;
          left: 0;
          width: 100%;
          height: 1.5px;
          background: linear-gradient(
            to right,
            rgba(0, 0, 0, 0) 0%,
            #FFFFFF 51.92%,
            rgba(0, 0, 0, 0) 100%
          );
        }
      }

      @media (max-width: 1350px) {
        font-size: 28px;
        line-height: 26px;
      }

      @media (max-width: 1100px) {
        font-size: 24px;
        line-height: 22px;
        gap: 0.25rem;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 18px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        background-color: #7846F9;
        font-size: 18px;
        white-space: nowrap;
        border-radius: 10px !important;
        font-weight: 800;
        text-transform: uppercase;
        box-shadow: 2.23px 4.83px 8.32px 0px #FFFFFF40 inset !important;
        filter: drop-shadow(0 0 0.5rem rgba(255, 255, 255, 0.50));

        @media (max-width: 1100px) {
          font-size: 18px;
        }

        @media (max-width: 800px) {
          white-space: wrap;
          font-size: 12px;
          max-width: 180px;
        }
      }
    }
  }
}

.edge-v22 {
  background-color: #000000;

  .banner-image {
    object-position: right;
  }

  .banner {
    .content {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      gap: 1rem;
      @media (max-width: 800px) {
        gap:0.5rem;
      }
    }

    .banner-header {
      font-family: Inter, Manrope, Manrope-fallback, sans-serif;
      font-size: 16px;
      font-weight: 500;
      color: #8FFDF6;
      line-height: 18px;
      white-space: wrap;
      position: relative;
      padding-right: 1rem;
      text-align: center;
      vertical-align: middle;
      text-transform: uppercase;
      width: min-content;
      letter-spacing: 0.25em;

      &::after {
        content: '';
        position: absolute;
        top: -10px;
        right: 0;
        width: 1px;
        height: 80px;
        background: linear-gradient(
          to bottom,
          rgba(0, 0, 0, 0) 0%,
          white,
          rgba(0, 0, 0, 0) 100%
        );
      }

      @media (max-width: 800px) {
        font-size: 10px;
        line-height: 16px;
        padding-right: 0.5rem;
        letter-spacing: 0;
      }
    }

    p {
      font-size: 40px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, Manrope, Manrope-fallback, sans-serif;
      margin-right: 1rem;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      gap: 0.5rem;
      position: relative;
      flex-wrap: wrap;

      @media (max-width: 1350px) {
        font-size: 32px;
        line-height: 26px;
      }

      @media (max-width: 1170px) {
        font-size: 24px;
        line-height: 22px;
        gap: 0.25rem;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 18px;
        padding-right: 0.5rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        color: black !important;
        background-color: #8FFDF6;
        font-size: 14px;
        white-space: nowrap;
        border-radius: 10px !important;
        font-weight: 800;
        text-transform: uppercase;
        box-shadow: 2.23px 4.83px 8.32px 0px #FFFFFF40 inset !important;
        filter: drop-shadow(0 0 0.5rem rgba(255, 255, 255, 0.50));
        padding: 0.75rem 1rem !important;

        @media (max-width: 800px) {
          white-space: wrap;
          font-size: 12px;
          max-width: 180px;
          padding: 0.5rem !important;
        }
      }
    }
  }
}

.matt-trial-v7 {
  background-color: #000000;

  .banner-image {
    background-position: right;
  }

  .banner {
    .content {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      gap: 1rem;
    }

    .banner-header {
      font-family: Inter, Manrope, Manrope-fallback, sans-serif;
      font-size: 14px;
      font-weight: 700;
      color: #33C26A;
      line-height: 16px;
      white-space: wrap;
      position: relative;
      padding: 0.5rem;
      text-align: center;
      border: 0.33px rgba(255, 255, 255, 0.5) solid;
      border-radius: 4px;
      box-shadow: -0.67px 0.8px 3.84px 1.67px #FFFFFF36 inset;
      max-width: 85px;

      @media (max-width: 800px) {
        font-size: 12px;
        line-height: 14px;
        padding: 0.25rem;
        max-width: 65px;
      }
    }

    p {
      color: white;
      font-size: 50px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, Manrope, Manrope-fallback, sans-serif;
      margin-right: 1rem;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      gap: 1rem;
      position: relative;
      flex-wrap: wrap;

      @media (max-width: 1200px) {
        font-size: 38px;
        line-height: 26px;
      }

      @media (max-width: 1040px) {
        font-size: 28px;
        line-height: 24px;
        gap: 0.25rem;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 18px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        background-color: #33C26A;
        font-size: 22px;
        white-space: nowrap;
        border-radius: 10px !important;
        font-weight: 800;
        text-transform: uppercase;
        box-shadow: 2.23px 4.83px 8.32px 0px #FFFFFF40 inset !important;
        filter: drop-shadow(0 0 0.5rem rgba(255, 255, 255, 0.50));
        padding: 0.75rem 1rem !important;

        @media (max-width: 1040px) {
          font-size: 18px;
        }

        @media (max-width: 800px) {
          white-space: wrap;
          font-size: 12px;
          max-width: 180px;
          padding: 0.5rem !important;
        }
      }
    }
  }
}
