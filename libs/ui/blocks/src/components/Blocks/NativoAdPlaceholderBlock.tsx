import React from 'react';
import { nativoAdManager, nativoAdWrapperClassnames } from '@benzinga/ads-utils';
import { useWindowLoad } from '@benzinga/hooks';
//import { useIntersectionObserver } from '@benzinga/hooks';

export interface NativoAdPlaceholderBlockProps {
  attrs: {
    className?: string;
    data: {
      type: string;
    };
  };
}

export const NativoAdPlaceholderBlock: React.FC<NativoAdPlaceholderBlockProps> = ({ attrs }) => {
  const adPlacementClassName = nativoAdWrapperClassnames[attrs?.data?.type || ''];
  const className = attrs?.className ? `${attrs?.className} ${adPlacementClassName}` : adPlacementClassName;

  useWindowLoad(() => {
    nativoAdManager.init(true);
  }, []);

  const ref = React.useRef<HTMLDivElement | null>(null);
  // const handleOnUpdate = (entry: IntersectionObserverEntry) => {
  //   if (entry.isIntersecting) {
  //     nativoAdManager.start();
  //   }
  // };

  // useIntersectionObserver(ref as React.RefObject<Element>, { freezeOnceVisible: true }, handleOnUpdate);

  return <div className={`${className}`} ref={ref}></div>;
};
