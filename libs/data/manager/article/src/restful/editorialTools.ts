import { RestfulClient, Session } from '@benzinga/session';
import { SafePromise, SafeError } from '@benzinga/safe-await';
import { ArticleEnvironment } from '../environment';
import {
  EditorialArticlePreviewResponse,
  TrendingIndiaPosts,
  TrendingIndiaTopics,
  WNSTNFollowUpQuestionsResponse,
} from '../entities/article';

export class EditorialToolsRestful extends RestfulClient {
  constructor(session: Session) {
    super(session.getEnvironment(ArticleEnvironment).editorialToolsUrl, session, {
      authorization: true,
    });
  }

  getTrendingIndiaStoriesIds = (): SafePromise<TrendingIndiaPosts[]> => {
    const url = this.URL(`api/trending-india/posts`);
    return this.get(url, {
      allowsAnonymousAuth: true,
      credentials: 'same-origin',
    });
  };

  getTrendingIndiaTopics = (): SafePromise<TrendingIndiaTopics[]> => {
    const url = this.URL(`api/trending-india/topics`);
    return this.get(url, {
      allowsAnonymousAuth: true,
      credentials: 'same-origin',
    });
  };

  getEditorialArticlePreview = (nodeId: number | string): SafePromise<EditorialArticlePreviewResponse> => {
    const key = this.session.getEnvironment(ArticleEnvironment).editorialPreviewKey;
    const hostname = this.session.getEnvironment(ArticleEnvironment).url;
    const url = this.URL(`${hostname}services/content/editorial/preview?key=${key}&nid=${nodeId}`);
    return this.get(url, {
      allowsAnonymousAuth: true,
      credentials: 'same-origin',
    });
  };

  getEditorialFollowUpsQuestionsInternal = (nodeId: number | string): SafePromise<WNSTNFollowUpQuestionsResponse> => {
    const hostname = this.session.getEnvironment(ArticleEnvironment).wnstnUrl;
    const url = this.URL(`${hostname}followups/${nodeId}`);
    return this.get(url, {
      allowsAnonymousAuth: true,
      credentials: 'same-origin',
    });
  };

  getEditorialFollowUpsQuestions = async (nodeId: number | string): SafePromise<WNSTNFollowUpQuestionsResponse> => {
    try {
      const response = await fetch(`/api/wnstn-followups?articleId=${nodeId}`);

      if (!response.ok) {
        return {
          err: new Error(`API request failed: ${response.status} ${response.statusText}`),
        };
      }

      const data = await response.json();
      return { ok: data };
    } catch (error) {
      return {
        err: error instanceof Error ? error : new Error('Failed to fetch follow-up questions'),
      };
    }
  };
}
