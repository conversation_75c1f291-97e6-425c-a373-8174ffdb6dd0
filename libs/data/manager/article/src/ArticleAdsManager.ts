import { DateTime } from 'luxon';
import { getConnatixBlock, getNativoAdBlock, getRaptiveAdBlock, getTaboolaBlock } from '@benzinga/ads-utils';
import { Block, MoneyBlockType, NodeLayout } from '@benzinga/content-manager';
import {
  countElementsInBodyBlocks,
  isTaxonomyExists,
  type ArticleBlock,
  type ArticleBlockPlaceholder,
  type ArticleData,
  type CampaignStrategy,
} from './index';
import { StoryCategory } from '@benzinga/advanced-news-manager';
import { DeviceType } from '@benzinga/device-utils';

type Blocks = ArticleBlock[] | ArticleBlockPlaceholder[] | MoneyBlockType[];

export interface ArticleAdsManagerOptions {
  nodeId?: number | string;
  contentType?: string;
  articleTitle?: string;
  articleBody?: string[];
  articleBlocks?: Blocks;
  articleUrl?: string;
  disableCampaignifyUnit?: boolean;
  hasAdLight?: boolean;
  isSponsored?: boolean;
  channels?: StoryCategory[];
  layout?: NodeLayout | null;
  createdDate?: string;
  deviceType?: DeviceType | null;
  tags?: StoryCategory[];
  ticker?: string;
  wordCount?: number | null;
  articleData?: ArticleData;
  googleAdPlaceholder?: Block;
  raptiveEnabled?: boolean;
  useNewTemplate?: boolean;
}

export class ArticleAdsManager {
  public readonly AD_BLOCK_NAMES = [
    'ad/campaignify',
    'acf/raptive-ad-placement',
    'acf/nativo-ad-placement',
    'ad/primis',
    'acf/connatix-live-block',
    'acf/taboola-placement',
  ];
  private readonly MIN_BLOCKS_BETWEEN_ADS = 3;
  private readonly NUMBER_OF_BODY_ADS = 5;
  private readonly MIN_PARAGRAPHS_BETWEEN_IMAGES = 2;
  private readonly SEARCH_RANGE = 20;
  private readonly MIN_PARAGRAPH_LENGTH = 130;
  private readonly MIN_REMAINING_BLOCKS = 2;
  private readonly SHORT_ARTICLE_WORD_COUNT = 200;
  private readonly FEW_PARAGRAPHS_ARTICLE_COUNT = 5;

  private readonly CONTENT_TYPE_FOR_HIDE = 'story';
  private readonly CHANNELS_FOR_HIDE = ['News', 'Markets'];
  private readonly COUNT_OF_ELEMENT_FOR_HIDE = 4;
  private readonly TYPE_OF_ELEMENT_FOR_HIDE = 'p';
  private readonly DAYS_OF_PUBLISHED_FOR_HIDE = 30;
  private readonly MINI_HEADER_TEXTS = ["Don't Miss:", 'Read Next:', 'Read More:', 'Read Now:'];

  private contentType?: string;
  private articleBlocks: Blocks;
  private articleUrl?: string;
  private channels?: StoryCategory[];
  private createdDate?: string;
  private deviceType?: DeviceType | null;
  private hasAdLight?: boolean;
  private isSponsored?: boolean;
  private layout?: NodeLayout | null;
  private strategy?: CampaignStrategy;
  private useNewTemplate?: boolean;
  private wordCount?: number | null;

  constructor(options: ArticleAdsManagerOptions) {
    this.contentType = options.contentType;
    this.articleBlocks = options.articleBlocks || [];
    this.articleUrl = options.articleUrl;
    this.channels = options.channels;
    this.createdDate = options.createdDate;
    this.deviceType = options.deviceType;
    this.hasAdLight = options.hasAdLight;
    this.isSponsored = options.isSponsored;
    this.layout = options.layout;
    this.useNewTemplate = options.useNewTemplate;
    this.wordCount = options.wordCount;
  }

  public excludeBlocks(blocks: Blocks): Blocks {
    if (!blocks) return [];
    return blocks.filter(block => !['acf/google-ad-placement'].includes(block.blockName || ''));
  }

  public checkIfShortArticle(count: number): boolean {
    return count ? count < this.SHORT_ARTICLE_WORD_COUNT : false;
  }

  public countParagraphsInBlocks(blocks: ArticleBlock[]): number {
    if (!Array.isArray(blocks)) return 0;

    return blocks.filter(block => {
      if (block?.tag !== 'p' || !block?.innerHTML) return false;

      const plainText = block.innerHTML.replace(/<[^>]*>/g, '').trim();

      if (!plainText) return false;

      // Skip mini headers (like "Read Next:", "Don't Miss:", etc.)
      if (this.MINI_HEADER_TEXTS.some(header => plainText.startsWith(header))) {
        return false;
      }

      // Skip image attributions
      const lowerText = plainText.toLowerCase();
      if (
        plainText.length < 50 &&
        (lowerText.startsWith('image via') ||
          lowerText.startsWith('photo via') ||
          lowerText.startsWith('image:') ||
          lowerText.startsWith('photo:') ||
          lowerText.includes('image via shutterstock') ||
          lowerText.includes('photo: shutterstock') ||
          lowerText.includes('image: shutterstock') ||
          lowerText.includes('image via getty') ||
          lowerText.includes('photo: getty') ||
          lowerText.includes('image: getty') ||
          lowerText.match(/^(photo|image)\s*(via|:)\s*/i))
      ) {
        return false;
      }

      return true;
    }).length;
  }

  public checkIfFewParagraphsArticle(blocks: ArticleBlock[]): boolean {
    const paragraphCount = this.countParagraphsInBlocks(blocks);
    return paragraphCount < this.FEW_PARAGRAPHS_ARTICLE_COUNT;
  }

  public isNearElement(
    blocks: ArticleBlock[],
    index: number,
    range: number,
    checkIndex: boolean,
    checkBefore: boolean,
    checkAfter: boolean,
    checkFunc: (block: ArticleBlock) => boolean,
  ): boolean {
    if (checkIndex && index < blocks.length && checkFunc(blocks[index])) {
      return true;
    }

    if (checkBefore) {
      for (let i = Math.max(0, index - range); i < index; i++) {
        if (i < blocks.length && checkFunc(blocks[i])) {
          return true;
        }
      }
    }

    if (checkAfter) {
      for (let i = index + 1; i <= Math.min(blocks.length - 1, index + range); i++) {
        if (checkFunc(blocks[i])) {
          return true;
        }
      }
    }

    return false;
  }

  public isNearImageOrEmbed(
    blocks: ArticleBlock[],
    index: number,
    range: number,
    checkBefore = true,
    checkAfter = true,
    type: 'all' | 'image' | 'embed' = 'all',
  ): boolean {
    const elementCheck = (block: ArticleBlock): boolean => {
      if (!block) return false;
      const isImage = !!block.tagAttributes?.className?.includes('wp-block-image');
      const isEmbed = !!block.tagAttributes?.className?.includes('wp-block-embed');
      return type === 'image' ? isImage : type === 'embed' ? isEmbed : isImage || isEmbed;
    };
    return this.isNearElement(blocks, index, range, true, checkBefore, checkAfter, elementCheck);
  }

  public isNearAd(
    blocks: ArticleBlock[],
    index: number,
    range: number,
    checkBefore = true,
    checkAfter = true,
  ): boolean {
    const adCheck = (block: ArticleBlock): boolean => {
      return block ? this.AD_BLOCK_NAMES.includes(block.blockName) : false;
    };
    return this.isNearElement(blocks, index, range, true, checkBefore, checkAfter, adCheck);
  }

  public isNearHeader(
    blocks: ArticleBlock[],
    index: number,
    range: number,
    checkBefore = true,
    checkAfter = false,
  ): boolean {
    const headerCheck = (block: ArticleBlock): boolean => {
      if (!block) return false;

      const isHeaderTag = block.tag && /h[1-6]/.test(block.tag);
      const isMiniHeaderText =
        !!block.innerHTML && this.MINI_HEADER_TEXTS.some(text => block.innerHTML?.includes(text));

      return isHeaderTag || isMiniHeaderText;
    };

    return this.isNearElement(blocks, index, range, false, checkBefore, checkAfter, headerCheck);
  }

  public isNearHorizontalRule(
    blocks: ArticleBlock[],
    index: number,
    range: number,
    checkBefore = true,
    checkAfter = false,
  ): boolean {
    return this.isNearElement(blocks, index, range, true, checkBefore, checkAfter, block => block?.tag === 'hr');
  }

  public isAppropriateForAdInsertion(blocks: ArticleBlock[], index: number, isFirstAd = false): boolean {
    if (index < 0 || index >= blocks.length) return false;

    const block = blocks[index];
    if (!block?.innerHTML) return false;

    if (isFirstAd) {
      return !this.isNearHeader(blocks, index, 1, true, true) && !this.isNearAd(blocks, index, 3);
    }

    if (
      this.isNearImageOrEmbed(blocks, index, this.MIN_PARAGRAPHS_BETWEEN_IMAGES) ||
      this.isNearAd(blocks, index, this.MIN_BLOCKS_BETWEEN_ADS) ||
      this.isNearHeader(blocks, index, 1, true, false) ||
      this.isNearHorizontalRule(blocks, index, 1, true, true)
    ) {
      return false;
    }

    const plainText = block.innerHTML.replace(/<[^>]*>/g, '').trim();

    if (
      (block.tag && ['ul', 'ol', 'hr', 'figure'].includes(block.tag)) ||
      (block.tag === 'p' && plainText.length < this.MIN_PARAGRAPH_LENGTH)
    ) {
      return false;
    }

    return blocks.length - index >= this.MIN_REMAINING_BLOCKS;
  }

  public findNearestAppropriatePosition(
    blocks: ArticleBlock[],
    targetIndex: number,
    usedPositions: number[] = [],
    onlyCheckForward = false,
  ): number {
    const totalBlocks = blocks.length;

    if (
      targetIndex >= 0 &&
      targetIndex < totalBlocks &&
      this.isAppropriateForAdInsertion(blocks, targetIndex) &&
      !usedPositions.includes(targetIndex)
    ) {
      return targetIndex;
    }

    for (let i = 1; i < this.SEARCH_RANGE; i++) {
      const forward = targetIndex + i;
      if (
        forward < totalBlocks &&
        this.isAppropriateForAdInsertion(blocks, forward) &&
        !usedPositions.includes(forward)
      ) {
        return forward;
      }

      if (!onlyCheckForward) {
        const backward = targetIndex - i;
        if (backward >= 0 && this.isAppropriateForAdInsertion(blocks, backward) && !usedPositions.includes(backward)) {
          return backward;
        }
      }
    }

    return -1;
  }

  public findOptimalAdPositions(blocks: ArticleBlock[], numAds: number): number[] {
    if (!blocks || !blocks.length) return [];

    const positions: number[] = [];
    let lastPosition = -1;

    const FIRST_AD_INDEX = 2;
    for (let i = FIRST_AD_INDEX; i < Math.min(blocks.length, FIRST_AD_INDEX + this.MIN_BLOCKS_BETWEEN_ADS); i++) {
      positions.push(i);
      lastPosition = i;
      break;
    }

    let nextPosition = this.findNearestAppropriatePosition(
      blocks,
      lastPosition + this.MIN_BLOCKS_BETWEEN_ADS,
      positions,
    );

    while (positions.length < numAds && lastPosition < blocks.length) {
      if (nextPosition === -1 || nextPosition >= blocks.length) {
        break;
      }

      if (positions.length > 0 && nextPosition - lastPosition < this.MIN_BLOCKS_BETWEEN_ADS) {
        // Force search to start at a minimum safe distance and only look forward
        nextPosition = this.findNearestAppropriatePosition(
          blocks,
          lastPosition + this.MIN_BLOCKS_BETWEEN_ADS,
          positions,
          true,
        );
        continue;
      }

      positions.push(nextPosition);
      lastPosition = nextPosition;

      nextPosition = this.findNearestAppropriatePosition(blocks, nextPosition + this.MIN_BLOCKS_BETWEEN_ADS, positions);
    }

    return positions;
  }

  public getAdBlock(position: number, adPositions: number[], isMobile: boolean): ArticleBlock {
    const positionIndex = adPositions.findIndex(pos => pos === position);
    const channelNames = this.channels?.map(channel => channel.name);

    const mobileAdPositions = {
      0: getRaptiveAdBlock(),
      1: getConnatixBlock({ channels: channelNames, ...this.layout?.video_player }),
      2: getTaboolaBlock({
        mode: 'rec-reel-2n5-a',
        placement: 'OC Reco-Reel',
        settings: {
          photo: 'auto',
          target_type: 'mix',
          url: this.articleUrl,
        },
      }),
      3: getNativoAdBlock(),
    };

    const desktopAdPositions = {
      0: getRaptiveAdBlock(),
      1: getConnatixBlock({ channels: channelNames, ...this.layout?.video_player }),
      2: getNativoAdBlock(),
    };

    if (this.useNewTemplate) {
      if (isMobile && mobileAdPositions[positionIndex]) {
        return mobileAdPositions[positionIndex];
      } else if (desktopAdPositions[positionIndex]) {
        return desktopAdPositions[positionIndex];
      }
    }

    return getRaptiveAdBlock() as ArticleBlock;
  }

  public insertAdsIntoArticle(blocks: ArticleBlock[]) {
    const isMobile = this.deviceType === 'mobile';
    const numberOfBodyAds = isMobile ? this.NUMBER_OF_BODY_ADS + 1 : this.NUMBER_OF_BODY_ADS;
    const adPositions = this.findOptimalAdPositions(blocks, numberOfBodyAds);
    let raptiveAdCount = 0;
    let totalAdCount = 0;

    // Sort positions in descending order to maintain correct indices when inserting
    const sortedPositions = [...adPositions].sort((a, b) => b - a);

    sortedPositions.forEach(position => {
      const adBlock = this.getAdBlock(position, adPositions, isMobile);
      blocks.splice(position, 0, adBlock);

      if (adBlock?.blockName === 'acf/raptive-ad-placement') {
        raptiveAdCount++;
      }
      totalAdCount++;
    });

    return {
      amountOfAds: totalAdCount,
      amountOfRaptiveAds: raptiveAdCount,
      blocks,
    };
  }

  public insertBelowArticleVideo(blocks: ArticleBlock[]): ArticleBlock[] {
    const channelNames = this.channels?.map(channel => channel.name);
    const videoBlock = getConnatixBlock({
      channels: channelNames,
      ...this.layout?.video_player,
    }) as ArticleBlock;

    return [...blocks, videoBlock];
  }

  public shouldHideCampaign(): boolean {
    if (!this.createdDate || !this.articleBlocks || !this.contentType || !this.channels) {
      return false;
    }

    const duration = DateTime.now().diff(DateTime.fromISO(this.createdDate), ['days']);

    return (
      this.contentType === this.CONTENT_TYPE_FOR_HIDE &&
      isTaxonomyExists(this.CHANNELS_FOR_HIDE, this.channels) &&
      countElementsInBodyBlocks(this.articleBlocks as ArticleBlock[], this.TYPE_OF_ELEMENT_FOR_HIDE) >
        this.COUNT_OF_ELEMENT_FOR_HIDE &&
      duration.days > this.DAYS_OF_PUBLISHED_FOR_HIDE
    );
  }

  public shouldRenderCampaign(content: string[] | Blocks): boolean {
    return (
      Array.isArray(content) &&
      content.length > 0 &&
      !this.isSponsored &&
      this.strategy !== 'none' &&
      !this.shouldHideCampaign()
    );
  }

  public shouldRenderAds(content: string[] | Blocks): boolean {
    return Array.isArray(content) && content.length > 0 && !this.isSponsored;
  }

  public findLastReadMorePosition(blocks: ArticleBlock[]): number {
    if (!blocks || !blocks.length) return -1;

    for (let i = blocks.length - 1; i >= 0; i--) {
      const content = blocks[i].innerHTML?.toLowerCase() || '';
      if (
        content.includes('<strong>read next:') ||
        content.includes('<strong>read more:') ||
        content.includes('<strong>read now:')
      ) {
        return i;
      }
    }
    return -1;
  }

  public formatArticleBlocks(blocks: Blocks): Blocks {
    if (!blocks || !blocks.length) return blocks;

    let newArticleBlocks: (MoneyBlockType | ArticleBlock)[] = [...blocks];

    if (this.shouldRenderAds(blocks)) {
      const isShortArticle = this.wordCount ? this.checkIfShortArticle(this.wordCount) : false;
      const isFewParagraphsArticle = this.checkIfFewParagraphsArticle(newArticleBlocks as ArticleBlock[]);
      if (isFewParagraphsArticle && !this.hasAdLight) {
        newArticleBlocks = this.insertBelowArticleVideo(newArticleBlocks as ArticleBlock[]);
        return newArticleBlocks;
      }
      if (!isShortArticle && !this.hasAdLight) {
        const result = this.insertAdsIntoArticle(newArticleBlocks as ArticleBlock[]);
        newArticleBlocks = result.blocks;
      }
    }

    return newArticleBlocks;
  }

  public formatArticle(): Blocks {
    return this.formatArticleBlocks(this.excludeBlocks(this.articleBlocks) || []);
  }
}

export default ArticleAdsManager;
