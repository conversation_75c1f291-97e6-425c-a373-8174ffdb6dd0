import { Subscribable, Subscription } from '@benzinga/subscribable';
import { SafeError, SafePromise } from '@benzinga/safe-await';
import { Authentication, RegisterUser } from './entities';
import { getValueFromCookie } from '@benzinga/utils';
import { AuthenticationRequest, AuthenticationRequestEvent } from './request';
import { Session } from '../../session';
import { AuthenticationStore, AuthenticationStoreEvent } from './store';
import { AuthenticationEnvironment } from './environment';
import { isBrowser } from '@benzinga/device-utils';
import { oneRequestAtATimeMultipleArgs } from '@benzinga/utils';

// Define a generic type for the debounced function
type DebouncedFunction<T extends (...args: any[]) => any> = (...args: Parameters<T>) => Promise<ReturnType<T>>;

export function debounce<T extends (...args: any[]) => any>(fn: T, delay: number): DebouncedFunction<T> {
  let timeoutId: NodeJS.Timeout | null = null;
  let pendingCalls: Array<(value: ReturnType<T>) => void> = [];

  const debouncedFn: DebouncedFunction<T> = (...args: Parameters<T>) => {
    return new Promise<ReturnType<T>>(resolve => {
      pendingCalls.push(resolve);

      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      timeoutId = setTimeout(async () => {
        const result = await fn(...args);
        pendingCalls.forEach(resolve => resolve(result));
        pendingCalls = [];
      }, delay);
    });
  };

  return debouncedFn;
}

export type AuthMode = 'login' | 'register' | 'sms-verify' | 'welcome' | 'complete' | 'upsell' | 'report-offer';

export interface RequestAuthEvent {
  type: 'request_auth';
  mode: AuthMode;
}

interface AuthorizedEvent {
  type: 'authorized';
}

interface PostLoginGetSessionEvent {
  type: 'authentication:post_login_get_session';
}

interface UnAuthorizedEvent {
  type: 'unauthorized';
}

export type AuthenticationManagerEvent =
  | AuthenticationRequestEvent
  | AuthenticationStoreEvent
  | RequestAuthEvent
  | AuthorizedEvent
  | UnAuthorizedEvent
  | PostLoginGetSessionEvent;

declare global {
  interface Window {
    google: any;
  }
}

/**
 * Authentication manager
 *
 * Deals with authentication in the SDK
 * This manager is always available as one of the core managers
 *
 * All other managers that work with Benzinga API will not work until
 * you authenticate using this manager
 *
 * Use your Benzinga API key as 'apiKey' environment variable for this manager to use API key authentication - in this case no further invocations of that managers are needed
 *
 * @export
 * @class AuthenticationManager
 * @extends {Subscribable<AuthenticationManagerEvent>}
 */
export class AuthenticationManager extends Subscribable<AuthenticationManagerEvent> {
  private static oneTapInitialized = false;
  private request: AuthenticationRequest;
  private store: AuthenticationStore;
  private storeSubscription?: Subscription<AuthenticationStore>;
  private requestSubscription?: Subscription<AuthenticationRequest>;
  private refreshTimeout?: ReturnType<typeof setInterval>;
  private apiKey?: string;

  private debouncedGetAuthSession = oneRequestAtATimeMultipleArgs(
    (fetch?: boolean, token?: string): SafePromise<Authentication> => {
      return this.setAuthentication(fetch, () => this.request.session(token));
    },
  );

  constructor(session: Session) {
    super();
    this.request = new AuthenticationRequest(session);
    this.store = new AuthenticationStore();
    this.apiKey = session.getEnvironment(AuthenticationEnvironment).apiKey || undefined;
    const token = this.getBenzingaToken();

    setTimeout(() => {
      if (token || token === '') {
        this.getAuthSessionNoLimit(true, token);
      }
    }, 0);

    if (isBrowser()) {
      if (typeof BroadcastChannel === 'undefined') return;
      const channel = new BroadcastChannel('auth-manager');
      channel.onmessage = event => {
        if (event.data && event.data.type === 'logout') {
          this.internalLogout();
        }
      };
    }
  }

  static getName = () => 'benzinga-authentication';

  /**
   * @internal
   *
   * @static
   * @memberof AuthenticationManager
   */
  public static getBenzingaTokenFromSystem = () => AuthenticationManager.getBenzingaTokenFromCookie(); // ?? AuthenticationManager.getBenzingaTokenFromLocalStorage();

  /**
   * @internal
   *
   * @private
   * @static
   * @memberof AuthenticationManager
   */
  private static getBenzingaTokenFromCookie = (): string | undefined => getValueFromCookie('benzinga_token');

  // This is a hack for the CI environment
  // private static getBenzingaTokenFromLocalStorage = (): string | undefined => {
  //   if (typeof localStorage !== `undefined`) {
  //     return localStorage.getItem('benzingaToken') ?? undefined;
  //   }
  //   return undefined;
  // };

  public setFingerprint = (fingerprint: unknown): void => this.store.setFingerprint(fingerprint);

  public getAuthentication = (): Authentication | undefined => this.store.getAuthentication();

  public getAuthSession = async (fetch?: boolean, token?: string): SafePromise<Authentication> => {
    return this.debouncedGetAuthSession(fetch, token);
  };

  public postLoginGetSession = async (fetch?: boolean, token?: string): SafePromise<Authentication> => {
    this.dispatch({
      type: 'authentication:post_login_get_session',
    });
    return this.getAuthSession(fetch, token);
  };

  public googleAuthorized = async (accessToken: string): SafePromise<Authentication> => {
    return this.setAuthentication(false, () => this.request.googleOneTapLogin(accessToken, '/api/v1/account/session/'));
  };

  /**
   * Perform login with given username and password
   *
   * On success, will return Authentication object and persist the session data
   * Which means that other APIs can be used
   *
   * @param {string} username
   * @param {string} password
   * @memberof AuthenticationManager
   */
  public login = async (username: string, password: string, captcha?: string): SafePromise<Authentication> => {
    if (!this.isLoggedIn()) {
      const auth = await this.request.login(username, password, this.store.getFingerprint(), captcha);
      if (auth.ok) {
        this.store.updateAuthenticationSession(auth.ok);
      }
      this.didAuthorize();
      return auth;
    }
    return { err: new SafeError('already logged in', 'authentication_manager') };
  };

  /**
   * Perform a logout and clear all session data
   *
   * Will return an error if there is no session data (not logged in)
   *
   * @memberof AuthenticationManager
   */
  public logout = async () => {
    if (this.isLoggedIn()) {
      const auth = await this.request.logout();
      this.store.updateAuthenticationSession(auth.ok);
      return auth;
    }
    return { err: new SafeError('not logged in', 'authentication_manager') };
  };

  /**
   * Register a new user
   *
   * @param {RegisterUser} user
   * @memberof AuthenticationManager
   */
  public register = async (user: RegisterUser, register_type?: string): SafePromise<Authentication> => {
    const auth = await this.request.register(user, {
      fingerprint: this.store.getFingerprint(),
      register_type: register_type,
    });
    if (auth.ok) {
      this.store.updateAuthenticationSession(auth.ok);
    }
    this.didAuthorize();
    return auth;
  };

  /**
   * Send request for forgotten password
   *
   * @param {string} email
   * @memberof AuthenticationManager
   */
  public forgotPassword = async (email: string): SafePromise<undefined> => {
    return await this.request.forgotPassword(email);
  };

  /**
   * Change password
   *
   * Must be logged in to call this method
   *
   * @param {string} currentPassword
   * @param {string} newPassword
   * @memberof AuthenticationManager
   */
  public changePassword = async (currentPassword: string, newPassword: string): SafePromise<undefined> => {
    if (this.isLoggedIn()) {
      return await this.request.changePassword(currentPassword, newPassword);
    }
    return { err: new SafeError('must be logged in to change password', 'authentication_manager') };
  };

  /**
   * Are you currently logged in?
   *
   * @memberof AuthenticationManager
   */
  public isLoggedIn = (): boolean => {
    const auth = this.store.getAuthentication();
    if (auth) {
      return auth.user.accessType !== 'anonymous';
    }
    // the cookie check is meant to be a quick check so we dont have to make a session call
    // the above check is somewhat redundant but it is a more accurate check
    // we may want to remove the cookie check in the future
    // and split it into it's own method, perhaps isLoggedInWithCookie
    const accessType = getValueFromCookie('bz_access_type');
    if (accessType) {
      return accessType !== 'anonymous';
    }
    return false;
  };

  /**
   * Are you currently logged in? Using just the cookie, may be inaccurate
   *
   * @memberof AuthenticationManager
   */
  public isLoggedInWithCookie = (): boolean => {
    // remove this comment if this has been implemented in the necessary places ex. login button in top bar
    // bz_access_type is set to anonymous if the user is not logged in, the other values
    // indicate their pro access type
    // regardless of the value, if it is not anonymous, they are logged in
    const accessType = getValueFromCookie('bz_access_type');
    if (accessType) {
      return accessType !== 'anonymous';
    }
    return false;
  };

  /**
   * Returns true if 'apiKey' was supplied in the environment, which means
   * we are using Licensing API keys
   *
   * @internal
   * @memberof AuthenticationManager
   */
  public isUsingApiKey = (): boolean => {
    return !!this.apiKey;
  };

  /**
   * @internal
   *
   * @memberof AuthenticationManager
   */
  public getApiKey = (): string | undefined => {
    return this.apiKey;
  };

  /**
   * @internal
   *
   * @memberof AuthenticationManager
   */
  public getBenzingaToken = (): string | undefined => {
    return this.store.getAuthentication()?.key ?? AuthenticationManager.getBenzingaTokenFromSystem();
  };

  /**
   * @internal
   *
   * @memberof AuthenticationManager
   */
  public getCSRFToken = (): string | undefined => {
    return this.store.getAuthentication()?.csrfToken;
  };

  /**
   * @internal
   *
   * @memberof AuthenticationManager
   */
  public getCoralJWT = (): string | null | undefined => {
    return this.store.getAuthentication()?.user?.coralJWT;
  };

  // this is only called if you don't login using the authentication manger
  // and don't have access to cookies
  public removeBenzingaToken = (): void => {
    this.store.updateAuthenticationSession(undefined);
  };

  // this is only called if you don't login using the authentication manger
  // and don't have access to cookies
  public setBenzingaToken = async (token: string): SafePromise<Authentication> => {
    if (token !== this.store.getAuthentication()?.key) {
      const auth = await this.request.session(token);
      if (auth.ok) {
        this.store.updateAuthenticationSession(auth.ok);
      } else {
        return auth;
      }
    }
    const auth = this.store.getAuthentication();
    if (auth) {
      return { ok: auth };
    } else {
      return {
        err: new SafeError('Sno authentication set in etBenzingaToken This should not be possible', 'authentication'),
      };
    }
  };

  public requestAuthorization(mode?: AuthMode): void {
    this.dispatch({
      mode: mode ?? 'register',
      type: 'request_auth',
    });
  }

  public didAuthorize(): void {
    this.dispatch({
      type: 'authorized',
    });
  }

  public didUnAuthorize(): void {
    this.dispatch({
      type: 'unauthorized',
    });
  }

  /**
   * Confirm SMS with a token
   *
   * @param {string} token
   * @memberof AuthenticationManager
   */
  public smsConfirm = async (token: string): SafePromise<boolean> => {
    return this.request.smsConfirm(token);
  };

  /**
   * Request SMS confirmation
   *
   * @memberof AuthenticationManager
   */
  public smsRequest = async (phone_number): SafePromise<boolean> => {
    return this.request.smsRequest(phone_number);
  };

  public emailRequest = async (): SafePromise<boolean> => {
    return this.request.emailRequest();
  };

  public humanConfirm = async (token: string): SafePromise<boolean> => {
    return this.request.humanConfirm(token);
  };

  public humanRequest = async (): SafePromise<boolean> => {
    return this.request.humanRequest();
  };

  public humanCheck = async (): SafePromise<boolean> => {
    return this.request.humanCheck();
  };

  public verifyNewEmail = async (newEmail: string): SafePromise<boolean> => {
    return this.request.verifyNewEmail(newEmail);
  };

  /**
   * One tap login helper for mobile apps
   * @param force - force update auth
   * @param authCallback - callback returning promise to retrive authentication obj
   */
  public injectAuthForMobileOneTapLogin = async (force: boolean, authCallback: () => SafePromise<Authentication>) => {
    return this.setAuthentication(force, authCallback);
  };

  protected onFirstSubscription(): void {
    this.requestSubscription = this.request.listen(event => this.dispatch(event));
    this.storeSubscription = this.store.listen(event => this.dispatch(event));
  }

  protected onZeroSubscriptions(): void {
    this.requestSubscription?.unsubscribe();
    this.requestSubscription = undefined;
    this.storeSubscription?.unsubscribe();
    this.storeSubscription = undefined;
  }

  private internalLogout = (): void => {
    if (this.isLoggedIn()) {
      // This is a hack for the CI environment
      // this.removeBenzingaToken();
      if (isBrowser()) {
        sessionStorage.clear();
      }
      if (this.refreshTimeout) {
        clearTimeout(this.refreshTimeout);
      }
      this.refreshTimeout = undefined;
      this.getAuthSession(true);
    }
  };

  private getAuthSessionNoLimit = async (fetch?: boolean, token?: string): SafePromise<Authentication> => {
    return this.setAuthentication(fetch, () => this.request.session(token));
  };

  private refresh = async () => {
    const refreshTimer = (time: number) => {
      if (this.refreshTimeout) {
        clearTimeout(this.refreshTimeout);
      }
      this.refreshTimeout = setTimeout(this.refresh, time);
    };

    const auth = await this.request.refresh();
    if (auth.err) {
      if ([401, 403].includes((auth.err?.data as any).statusCode ?? 0)) {
        this.refreshTimeout = undefined;
        if (this.isLoggedIn()) {
          this.logout();
        }
      } else {
        refreshTimer(120000);
      }
    } else if (auth.ok && this.store.getAuthentication()) {
      if (this.store.refreshAuthenticationSession(auth.ok)) {
        this.refreshTimeout = undefined;
        this.getAuthSession(true);
      } else {
        // refresh returns secs and Date.now returns millSecs we are simply refreshing 30 secs before the timeout.
        refreshTimer(Math.max((auth.ok.exp - Date.now() / 1000 - 30) * 1000, 120000));
      }
    } else {
      if (this.isLoggedIn()) {
        refreshTimer(120000);
      } else {
        this.refreshTimeout = undefined;
      }
    }
    return auth;
  };

  private setAuthentication = async (
    force: boolean | undefined,
    callback: () => SafePromise<Authentication>,
  ): SafePromise<Authentication> => {
    const currentAuth = this.store.getAuthentication();
    // if no authentication is set or force is true, then we will set the authentication
    if (currentAuth === undefined || force) {
      const auth = await callback();
      if (auth.ok) {
        this.store.updateAuthenticationSession(auth.ok);
        if (this.refreshTimeout === undefined && this.isLoggedIn()) {
          this.refresh();
        }
      }
      return auth;
    } else if (currentAuth && currentAuth.user.accessType === 'anonymous') {
      return { err: new SafeError('not logged in', 'authentication_manager') };
    }
    return { err: new SafeError('already logged in', 'authentication_manager') };
  };
}
