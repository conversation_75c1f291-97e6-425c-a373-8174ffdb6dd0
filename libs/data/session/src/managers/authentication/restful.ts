import { SafeError, safeMap, SafePromise } from '@benzinga/safe-await';
import { RestfulClient } from '../../utils';
import { Session } from '../../session';
import { EgressRegisterUser } from './entities';
import {
  IngressAuthentication,
  IngressRefreshResponse,
  IngressSmsResponse,
  IngressHumanResponse,
  IngressEmailResponse,
} from './entities';
import { AuthenticationEnvironment } from './environment';
import { getRegisterType, runningClientSide } from '@benzinga/utils';
import { getLanguageCodeByHost } from '@benzinga/translate';
import { getRegisterTypeByIdentity } from '@benzinga/identity';

export class AuthenticationRestful extends RestfulClient {
  constructor(session: Session) {
    super(session.getEnvironment(AuthenticationEnvironment).url, session);
  }

  public googleOneTapLogin = (idToken: string, redirectUrl: string): SafePromise<IngressAuthentication> => {
    let registerType = 'unknown';
    const currentUrl = location.href;
    const encodedHref = encodeURI(location.href);
    let locationUrl = currentUrl;
    const urlParams = new URLSearchParams(location.search);
    const utmSource = urlParams.get('utm_source');
    const nextParam = urlParams.get('registernext') || urlParams.get('next') || urlParams.get('redirect');

    if (nextParam && nextParam !== '') {
      locationUrl = nextParam;
    } else if (!nextParam && locationUrl.includes('/login') && document?.referrer && document?.referrer !== '') {
      // use referrer as a default value if user is coming from login page to get the page prior
      locationUrl = document.referrer;
    }

    const registerTypeByIdentity =
      getRegisterTypeByIdentity(location.hostname, getLanguageCodeByHost(location.hostname)) ?? '';

    // if locationUrl is 'mobile_app', use it as registerType
    if (locationUrl === 'mobile_app') {
      registerType = locationUrl;
    }
    // if utm_source is present, use it as register_type
    else if (utmSource) {
      registerType = utmSource;
    } else if (locationUrl.includes('probeta.') || locationUrl.includes('pro.')) {
      registerType = 'benzingapro';
    } else if (registerTypeByIdentity) {
      registerType = registerTypeByIdentity ?? '';
    } else if (locationUrl && locationUrl !== '') {
      if (locationUrl.startsWith('/')) {
        locationUrl = 'https://www.benzinga.com' + locationUrl;
      } else if (!locationUrl.startsWith('http')) {
        locationUrl = 'https://www.benzinga.com/' + locationUrl;
      }
      const routeAsPath = new URL(locationUrl).pathname;
      registerType = getRegisterType(routeAsPath);
    }
    const url = this.URL('oauth/complete/google-one-tap/', {
      id_token: idToken,
      next: redirectUrl,
      register_type: registerType,
      url: encodedHref,
    });
    return this.get(url, {
      allowsAnonymousAuth: true,
    });
  };

  public login = (
    email: string,
    password: string,
    fingerprint?: unknown,
    captcha?: string,
  ): SafePromise<IngressAuthentication> => {
    const encodedHref = typeof location !== 'undefined' ? encodeURI(location.href) : 'mobile_app';
    const url = this.URL('api/v1/account/login/', {
      include_layout_list: true,
      include_perms: true,
      ...(captcha && { captcha }),
      url: encodedHref,
    });
    return this.post(url, { email, fingerprint, password }, { allowsAnonymousAuth: true });
  };

  public logout = (): SafePromise<undefined> => {
    const url = this.URL('api/v1/account/logout/');
    return safeMap(this.getRaw(url), () => undefined);
  };

  public forgotPassword = (email: string): SafePromise<undefined> => {
    const url = this.URL('api/v1/account/reset/password/request/');
    return this.post(url, { email }, { allowsAnonymousAuth: true });
  };

  public changePassword = (currentPassword: string, newPassword: string): SafePromise<undefined> => {
    const url = this.URL('api/v1/account/change/password/');
    return this.post(url, { current_password: currentPassword, new_password: newPassword });
  };

  public register = (user: EgressRegisterUser): SafePromise<IngressAuthentication> => {
    let registerType = user.register_type;

    if (!registerType || registerType === '' || registerType === 'unknown') {
      let locationUrl = location.href;
      const urlParams = new URLSearchParams(location.search);
      const utmSource = urlParams.get('utm_source');
      const nextParam = urlParams.get('registernext') || urlParams.get('next') || urlParams.get('redirect');

      if (nextParam && nextParam !== '') {
        locationUrl = nextParam;
      } else if (!nextParam && locationUrl.includes('/login') && document?.referrer && document?.referrer !== '') {
        // use referrer as a default value if user is coming from login page to get the page prior
        locationUrl = document.referrer;
      }

      const registerTypeByIdentity =
        getRegisterTypeByIdentity(location.hostname, getLanguageCodeByHost(location.hostname)) ?? '';

      // if utm_source is present, use it as register_type
      if (utmSource) {
        registerType = utmSource;
      } else if (locationUrl.includes('probeta.') || locationUrl.includes('pro.')) {
        registerType = 'benzingapro';
      } else if (registerTypeByIdentity) {
        registerType = registerTypeByIdentity;
      } else if (locationUrl && locationUrl !== '') {
        if (locationUrl.startsWith('/')) {
          locationUrl = 'https://www.benzinga.com' + locationUrl;
        } else if (!locationUrl.startsWith('http')) {
          locationUrl = 'https://www.benzinga.com/' + locationUrl;
        }
        const routeAsPath = new URL(locationUrl).pathname;
        registerType = getRegisterType(routeAsPath);
      }

      user.register_type = registerType;
    }
    const encodedHref = typeof location !== 'undefined' ? encodeURI(location.href) : 'mobile_app';
    const url = this.URL('api/v1/account/register/', { include_perms: true, url: encodedHref });
    return this.post(url, user, { allowsAnonymousAuth: true });
  };

  public refresh = (token?: string): SafePromise<IngressRefreshResponse> => {
    const url = this.URL('api/v1/account/refresh/');
    if (token) {
      return this.post(url, { refresh: token }, { allowsAnonymousAuth: true });
    } else {
      return this.post(url, undefined, { allowsAnonymousAuth: true });
    }
  };

  public getSession = (token?: string): SafePromise<IngressAuthentication> => {
    const params = {
      allow_anonymous: true,
      include_perms: true,
      include_subs: true,
    };
    if (runningClientSide()) {
      const encodedHref = typeof location !== 'undefined' ? encodeURI(location.href) : 'mobile_app';
      params['url'] = encodedHref;
    }
    const url = this.URL('api/v1/account/session/', params);
    // TODO remove this once pro and bznext uses authManager to login

    return this.debouncedGet(url, {
      allowsAnonymousAuth: true,
      bzToken: token,
      includeHeader: { authorizationSession: !!token },
      resilience: {
        delayMultiple: 250,
        delayOffset: 10000,
        isError: result => {
          return new Promise(resolve => {
            if ((result as any)?.['detail'] != null) {
              resolve({ err: new SafeError('throttled', 'throttled', result) });
            } else {
              resolve({ ok: result });
            }
          });
        },
        maxDelay: 30000,
        retryOnError: true,
      },
    });
  };

  public smsCheck = (): SafePromise<IngressSmsResponse> => {
    const url = this.URL('api/v1/account/verify/sms/check/');
    return this.get(url);
  };

  public smsRequest = (phone_number): SafePromise<IngressSmsResponse> => {
    const url = this.URL('api/v1/account/verify/sms/request/');
    return this.post(url, { phone_number });
  };

  public smsConfirm = (token: string): SafePromise<IngressSmsResponse> => {
    const url = this.URL('api/v1/account/verify/sms/confirm/');
    return this.post(url, { token });
  };

  public emailRequest = (): SafePromise<IngressEmailResponse> => {
    const url = this.URL('api/v1/account/verify/email/request/');
    return this.post(url, undefined);
  };

  public humanCheck = (): SafePromise<IngressHumanResponse> => {
    const url = this.URL('api/v1/account/verify/human/check/');
    return this.get(url);
  };

  public humanRequest = (): SafePromise<IngressHumanResponse> => {
    const url = this.URL('api/v1/account/verify/human/request/');
    return this.post(url, undefined);
  };

  public humanConfirm = (token: string): SafePromise<IngressHumanResponse> => {
    const url = this.URL('api/v1/account/verify/human/confirm/');
    return this.post(url, { token });
  };

  public verifyNewEmail = (new_email: string): SafePromise<IngressEmailResponse> => {
    const url = this.URL('api/v1/account/verify/email/request_email_change/');
    return this.post(url, { new_email });
  };
}
