import type { GlobalRegisterType } from '@benzinga/identity';

import { UserSubscription } from './user-subscription';

export type AccessType = 'none' | 'manual' | 'subscribed' | 'trialing' | 'free' | 'anonymous';
interface PhoneInfo {
  countryAlpha3: string;
  countryCode: string;
  countryName: string;
  national: string;
}

export interface LayoutListItem {
  date_created: string;
  date_updated: string;
  ip_address: string;
  name: string;
  user_agent: string;
  uuid: string;
}

export interface UserMessage {
  displayType: 'modal' | 'banner';
  duration: 'timeout' | 'session';
  html: string;
  properties: {
    width: string;
    height: string;
  };
  required: boolean;
  timeout?: number;
  url: string;
  uuid: string;
}

export interface User {
  accessType: AccessType;
  benzingaUid: number;
  displayName: string;
  email: string;
  emailVerified: boolean;
  firstName: string;
  humanVerified: boolean;
  id: number;
  lastName: string;
  subscriptions?: UserSubscription[] | null;
  layout_list?: LayoutListItem[];
  messages: UserMessage[];
  profileData?: ProfileData;
  permissions?: Permission[];
  phoneInfo: PhoneInfo | null;
  phoneNumber: string;
  profileImage: string;
  smsVerified: boolean;
  tradeitToken: string;
  trialEndDate: string | null;
  uuid: string;
  hubspot_id: string;
  coralJWT: string | null;
  intercom_user_hmac: string;
}

export interface RegisterUser {
  displayName?: string;
  email: string;
  captcha?: string;
  firstName?: string;
  lastName?: string;
  password: string;
  passwordConfirmation?: string;
  phoneNumber?: string;
  proTrial?: boolean;
  profileData?: ProfileData;
}

export type languageType = 'EN' | 'ES' | 'IT' | 'FR' | 'JP' | 'JP' | 'KR';
export type subdomain = 'benzinga' | 'es' | 'it' | 'fr' | 'jp' | 'kr';

export interface ProfileData {
  globalAccountType?: GlobalRegisterType;
  language?: languageType;
  subdomain?: subdomain;
}

export interface Authentication {
  exp: number;
  csrfToken: string;
  key: string;
  user: User;
}

export interface RefreshResponse {
  exp: number;
  permissions: Permission[];
}

export interface Permission {
  action: string;
  effect?: 'allow' | 'deny';
  resource: string;
}

export interface GoogleAuthenticationResponse {
  client_id: string;
  credential: string;
  select_by: string;
}

export interface GooglePromptResponse {
  g: string;
  h: boolean;
}
